<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class UpdateStudentsSeeder extends Seeder
{
    public function run(): void
    {
        $now = Carbon::now();

        // Clear existing students first
        DB::table('students')->truncate();

        // Insert Batch 2025 Girls
        $batch2025_girls = [
            '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>, <PERSON><PERSON>', '<PERSON><PERSON>-as <PERSON><PERSON><PERSON>', '<PERSON><PERSON>, <PERSON>',
            '<PERSON><PERSON><PERSON>, <PERSON>', '<PERSON><PERSON>, <PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>', '<PERSON><PERSON>, <PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>, <PERSON>', '<PERSON><PERSON>, <PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>, <PERSON><PERSON>',
            '<PERSON><PERSON>, <PERSON>', '<PERSON>, <PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>, <PERSON><PERSON>', '<PERSON><PERSON>, <PERSON>',
            '<PERSON><PERSON>, <PERSON><PERSON>', '<PERSON><PERSON><PERSON>, <PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>', '<PERSON><PERSON><PERSON>, <PERSON>',
            '<PERSON><PERSON><PERSON>, <PERSON>', '<PERSON><PERSON><PERSON>, <PERSON><PERSON>', '<PERSON><PERSON><PERSON>, <PERSON><PERSON>', '<PERSON><PERSON><PERSON>, <PERSON>',
            '<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>', '<PERSON><PERSON><PERSON>, <PERSON>', '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>, <PERSON><PERSON>', '<PERSON><PERSON>, <PERSON> <PERSON>', '<PERSON>, <PERSON> <PERSON>', '<PERSON>, <PERSON><PERSON>',
            '<PERSON><PERSON>, <PERSON><PERSON>', '<PERSON>, <PERSON>', '<PERSON> <PERSON><PERSON>, <PERSON><PERSON>', '<PERSON>, <PERSON> <PERSON>',
            '<PERSON><PERSON><PERSON>, <PERSON>', '<PERSON><PERSON><PERSON>, <PERSON> <PERSON>', '<PERSON><PERSON>, <PERSON><PERSON> <PERSON>', '<PERSON><PERSON>, <PERSON> <PERSON>',
            '<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>', '<PERSON>, <PERSON><PERSON> <PERSON>'
        ];

        fore<PERSON> ($batch2025_girls as $name) {
            DB::table('students')->insert([
                'name' => $name,
                'gender' => 'Female',
                'batch' => 2025,
                'created_at' => $now,
                'updated_at' => $now
            ]);
        }

        // Insert Batch 2025 Boys
        $batch2025_boys = [
            'Caritan, Jincent', 'Sarmiento, Eduard Jhon', 'Jovita, Michael', 'Pagunsan, Alfe',
            'Apawan, Gwyn', 'Calub, Josh Harvie', 'Milabo, Angelito', 'Estelloro, Eupe',
            'Godinez, Renz', 'Parrocho, Angelo', 'Ejurango, Junrel', 'Montaño, Jenvier',
            'Baguio, Joshua', 'Kiskisan, Nathaniel', 'Condez, John Arnel', 'Ricacho, Norkent',
            'Agsalud, Radel', 'Catibod, Jun Clark', 'Crisostomo, Janno', 'Chavez, Mark Kevin',
            'Mendoza, Deniel', 'Novicio, Freddie', 'Ybañez, Jasper Drake', 'Paner, Dion',
            'Casaldan, Jhon Paul', 'Casas, Ricky'
        ];

        foreach ($batch2025_boys as $name) {
            DB::table('students')->insert([
                'name' => $name,
                'gender' => 'Male',
                'batch' => 2025,
                'created_at' => $now,
                'updated_at' => $now
            ]);
        }

        // Insert Batch 2026 Girls
        $batch2026_girls = [
            'Yncierto, Nadezhda Jade', 'Silorio, Mariel', 'Bugais, Angela Lourene', 'Edaño, Luane Mari',
            'Diaz, Chris Jane', 'Canillo, Joan', 'Eria, Ereca Joy', 'Lumayaga, Allysa Joyce',
            'Barro, Niña Kathleen', 'Gesim, Jella Mae', 'Judico, Kristel Jean', 'Bawic, Mariel Ann',
            'Cantarona, Monique', 'Belia, Justine Mae', 'Gamboa, Gina', 'Cañares, Reynelyn',
            'Gelbolingo, Danica', 'Torrechilla, Judy Mae', 'Faburada, Jassy', 'Panangganan, Angelica',
            'Sigbat, Cherry Ann', 'Gumanoy, Athena Grazia', 'Dignos, Precious', 'Soco, Angel Marie',
            'Rellita, Neziel Jean', 'Lagaras, Joanna Joy', 'Veliganio, Kristel', 'Villegas, Cleofe Mae',
            'Villadolid, Anna Rhea', 'Mangyao, April Jane', 'Fernandez, Hazel Mae', 'Villarosa, Michelle Ann',
            'Aliviado, Angel', 'Villadolid, Aiza'
        ];

        foreach ($batch2026_girls as $name) {
            DB::table('students')->insert([
                'name' => $name,
                'gender' => 'Female',
                'batch' => 2026,
                'created_at' => $now,
                'updated_at' => $now
            ]);
        }

        // Insert Batch 2026 Boys
        $batch2026_boys = [
            'Engaña, Riel Jake', 'Orozco, John Paul', 'Arellano, Vinzon', 'Goles, Julius',
            'Solon, Bryle', 'Mansanades, John Michael', 'Ruales, Klint', 'Sabandal, Milven',
            'Pacunla, Carlo', 'Sacnanas, Mary Gwen', 'Vicaran, Edemar', 'Navarro, Kent John',
            'Cernal, Vhenz', 'Gilles, Christopher', 'Fabrigar, Adrian', 'Reboquio, Albert',
            'Montano, Adriane', 'Naveo, Rohann James Matt', 'Reyes, Josef Dean Gerald', 'Alaud, Ivan Justine',
            'Pacilan, Vince', 'Escala, Joshua', 'Cisneros, Myko', 'Quijano, Van Johanzel',
            'Virtudazo, Cristian', 'Escoro, Evans Est', 'Pila, Jhon Xander', 'Erebias, Ronald',
            'Diendo, Justine', 'Monion, Elgine', 'Dimpas, Mohammad', 'Torres, John Mark',
            'Jabagat, Seth Andrey', 'Jagonoy, Zhards Nathan', 'Hemoros, Rex', 'Ursal, Jasper'
        ];

        foreach ($batch2026_boys as $name) {
            DB::table('students')->insert([
                'name' => $name,
                'gender' => 'Male',
                'batch' => 2026,
                'created_at' => $now,
                'updated_at' => $now
            ]);
        }
    }
}
