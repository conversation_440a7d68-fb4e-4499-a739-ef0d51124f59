body {
    font-family: '<PERSON>pins', sans-serif;
    margin: 0;
    padding: 0;
    font-size: 20px;
    background-color: #f8f8f8;
}

header {
    background-color: #22BBEA;
    color: white;
    padding: 45px;
}

.logo {
    margin-left: none;
}

.logo img {
    width: 500px;
    height: auto;
    margin-left: none;
}

h1.page-title {
    text-align: center; 
    margin-top: 20px; 
}

.container {
    display: flex;
}

.sidebar {
    width: 220px;
    background-color: #fa5408;
    padding: 20px;
    min-height: 100vh;
    border-right: 1px solid #ccc;
}

.sidebar h3 {
    margin-top: 0;
}

.sidebar ul {
    list-style: none;
    padding-left: 0;
}

.sidebar ul li {
    margin: 15px 0;
}

.sidebar ul li:hover {
    background: #e4733f;
    max-width: 100%;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.sidebar ul li a {
    text-decoration: none;
    color: #000000;
    padding: 10px;
    display: block;
    border-radius: 5px;
}

.content {
    flex: 1;
    padding: 30px;
    font-display: center;
}





.task-item {
    background: #f8fafc;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 10px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.task-item h4 {
    margin: 0 0 10px 0;
    color: var(--primary);
}

.task-item p {
    margin: 5px 0;
    color: var(--dark);
}

.task-status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 14px;
    margin-top: 10px;
}

.status-pending {
    background: var(--warning);
    color: white;
}

.status-completed {
    background: var(--success);
    color: white;
}

.status-in-progress {
    background: var(--primary);
    color: white;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Task Form Styles */
#taskFormContainer {
    margin-top: 20px;
    background: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    max-width: 550px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

#taskForm label {
    display: block;
    margin-top: 10px;
    font-weight: 500;
}

#taskForm input,
#taskForm select,
#taskForm textarea {
    width: 100%;
    padding: 10px;
    margin-top: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
}

#taskForm button[type="submit"],
#taskForm button[type="button"] {
    margin-top: 15px;
    padding: 10px 16px;
    font-weight: 600;
    color: black;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

#taskForm button[type="submit"] {
    background-color: #31b93c;
    color: black;
    margin-right: 10px;
}

#taskForm button[type="button"] {
    background-color: #e3342f;
    color: black;
}

/* Feedback Section Styles */
.feedback-section {
    margin-top: 40px;
    padding: 20px;
    background-color: #eaf7fb;
    border: 1px solid #ccc;
    border-radius: 10px;
}

.feedback-section h3 {
    font-family: 'Poppins', sans-serif;
    margin-bottom: 15px;
}

textarea {
    font-family: 'Poppins', sans-serif;
    width: 100%;
    padding: 10px;
    font-size: 16px;
    resize: vertical;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.custom-file-input input[type="file"] {
    display: none;
}

.custom-file-input label {
    display: inline-block;
    padding: 5px 10px;
    background-color: white;
    color: rgb(20, 20, 20);
    border-radius: 5px;
    cursor: pointer;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    border: 1px solid #3a3a3a;
}

.file-name {
    display: inline-block;
    margin-left: 10px;
    font-size: 16px;
    font-family: 'Poppins', sans-serif;
    color: #333;
}

button[type="submit"] {
    margin-top: 15px;
    background-color: #22BBEA;
    color: rgb(0, 0, 0);
    border: none;
    padding: 10px 18px;
    border-radius: 5px;
    cursor: pointer;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
}

button[type="submit"]:hover {
    background-color: #199cc9;
}

.hidden {
    display: none;
} 