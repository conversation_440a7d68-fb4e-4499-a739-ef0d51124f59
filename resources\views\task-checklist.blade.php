@extends('layouts.app')

@section('content')
<div class="container-fluid p-3">
  <div class="row justify-content-center">
    <div class="col-12">
      <div class="table-responsive">
        <table class="table table-bordered mb-0" style="font-size: 11px; border: 2px solid #000;">
          <thead>
            <tr>
              <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 4px; vertical-align: middle;">TASKS TO COMPLETE</th>
              <th colspan="8" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 3px; font-size: 10px;">
                DATE: <input type="date" id="week1_date" value="{{ $currentWeekStart->format('Y-m-d') }}" class="form-control form-control-sm d-inline-block" style="width: 100px; font-size: 9px;" onchange="updateWeekDates()">
              </th>
              <th colspan="8" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 3px; font-size: 10px;">
                DATE: <input type="date" id="week2_date" value="{{ $currentWeekStart->copy()->addWeek()->format('Y-m-d') }}" class="form-control form-control-sm d-inline-block" style="width: 100px; font-size: 9px;" onchange="updateWeekDates()">
              </th>
            </tr>
            <tr>
              <th class="text-center" style="width: 30px; border: 1px solid #000; font-size: 9px; padding: 2px;">MON</th>
              <th class="text-center" style="width: 30px; border: 1px solid #000; font-size: 9px; padding: 2px;">TUE</th>
              <th class="text-center" style="width: 30px; border: 1px solid #000; font-size: 9px; padding: 2px;">WED</th>
              <th class="text-center" style="width: 30px; border: 1px solid #000; font-size: 9px; padding: 2px;">THU</th>
              <th class="text-center" style="width: 30px; border: 1px solid #000; font-size: 9px; padding: 2px;">FRI</th>
              <th class="text-center" style="width: 30px; border: 1px solid #000; font-size: 9px; padding: 2px;">SAT</th>
              <th class="text-center" style="width: 30px; border: 1px solid #000; font-size: 9px; padding: 2px;">SUN</th>
              <th class="text-center" style="width: 60px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 9px; padding: 2px;">REMARKS</th>
              <th class="text-center" style="width: 30px; border: 1px solid #000; font-size: 9px; padding: 2px;">MON</th>
              <th class="text-center" style="width: 30px; border: 1px solid #000; font-size: 9px; padding: 2px;">TUE</th>
              <th class="text-center" style="width: 30px; border: 1px solid #000; font-size: 9px; padding: 2px;">WED</th>
              <th class="text-center" style="width: 30px; border: 1px solid #000; font-size: 9px; padding: 2px;">THU</th>
              <th class="text-center" style="width: 30px; border: 1px solid #000; font-size: 9px; padding: 2px;">FRI</th>
              <th class="text-center" style="width: 30px; border: 1px solid #000; font-size: 9px; padding: 2px;">SAT</th>
              <th class="text-center" style="width: 30px; border: 1px solid #000; font-size: 9px; padding: 2px;">SUN</th>
              <th class="text-center" style="width: 60px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 9px; padding: 2px;">REMARKS</th>
            </tr>
          </thead>
          <tbody>
            @php
              $kitchenTasks = $tasks->where('task_category', 'KITCHEN');
              $cleaningTasks = $tasks->where('task_category', 'GENERAL CLEANING');
            @endphp

            <!-- Kitchen Tasks -->
            @foreach($kitchenTasks as $index => $task)
            <tr>
              @if($index === 0)
              <td rowspan="{{ $kitchenTasks->count() }}" style="background-color: #28a745; color: white; vertical-align: middle; text-align: center; font-weight: bold; border: 1px solid #000; font-size: 10px; writing-mode: vertical-lr; text-orientation: mixed; width: 60px;">
                KITCHEN<br>2-3<br>WEEKS<br>BEFORE<br>THE<br>ASSIGNED<br>DAY
              </td>
              @endif
              <td class="task-cell">{{ $task->task_description }}</td>

              <!-- Week 1 Status -->
              @for($day = 0; $day < 7; $day++)
              <td class="day-cell">
                <div class="status-buttons">
                  <button type="button" class="status-btn check-btn {{ ($task->week1_status[$day] ?? '') === '✓' ? 'active' : '' }}"
                          data-task-id="{{ $task->id }}" data-week="1" data-day="{{ $day }}" data-status="✓">✓</button>
                  <button type="button" class="status-btn wrong-btn {{ ($task->week1_status[$day] ?? '') === '✗' ? 'active' : '' }}"
                          data-task-id="{{ $task->id }}" data-week="1" data-day="{{ $day }}" data-status="✗">✗</button>
                </div>
              </td>
              @endfor

              <!-- Week 1 Remarks -->
              <td class="remarks-cell">
                <textarea class="remarks-input" data-task-id="{{ $task->id }}" data-week="1">{{ $task->week1_remarks ?? '' }}</textarea>
              </td>

              <!-- Week 2 Status -->
              @for($day = 0; $day < 7; $day++)
              <td class="day-cell">
                <div class="status-buttons">
                  <button type="button" class="status-btn check-btn {{ ($task->week2_status[$day] ?? '') === '✓' ? 'active' : '' }}"
                          data-task-id="{{ $task->id }}" data-week="2" data-day="{{ $day }}" data-status="✓">✓</button>
                  <button type="button" class="status-btn wrong-btn {{ ($task->week2_status[$day] ?? '') === '✗' ? 'active' : '' }}"
                          data-task-id="{{ $task->id }}" data-week="2" data-day="{{ $day }}" data-status="✗">✗</button>
                </div>
              </td>
              @endfor

              <!-- Week 2 Remarks -->
              <td class="remarks-cell">
                <textarea class="remarks-input" data-task-id="{{ $task->id }}" data-week="2">{{ $task->week2_remarks ?? '' }}</textarea>
              </td>
            </tr>
            @endforeach

            <!-- General Cleaning Tasks -->
            @foreach($cleaningTasks as $index => $task)
            <tr>
              @if($index === 0)
              <td rowspan="{{ $cleaningTasks->count() }}" style="background-color: #28a745; color: white; vertical-align: middle; text-align: center; font-weight: bold; border: 1px solid #000; font-size: 10px; writing-mode: vertical-lr; text-orientation: mixed; width: 60px;">
                GENERAL<br>CLEANING
              </td>
              @endif
              <td class="task-cell">{{ $task->task_description }}</td>

              <!-- Week 1 Status -->
              @for($day = 0; $day < 7; $day++)
              <td class="day-cell">
                <div class="status-buttons">
                  <button type="button" class="status-btn check-btn {{ ($task->week1_status[$day] ?? '') === '✓' ? 'active' : '' }}"
                          data-task-id="{{ $task->id }}" data-week="1" data-day="{{ $day }}" data-status="✓">✓</button>
                  <button type="button" class="status-btn wrong-btn {{ ($task->week1_status[$day] ?? '') === '✗' ? 'active' : '' }}"
                          data-task-id="{{ $task->id }}" data-week="1" data-day="{{ $day }}" data-status="✗">✗</button>
                </div>
              </td>
              @endfor

              <!-- Week 1 Remarks -->
              <td class="remarks-cell">
                <textarea class="remarks-input" data-task-id="{{ $task->id }}" data-week="1">{{ $task->week1_remarks ?? '' }}</textarea>
              </td>

              <!-- Week 2 Status -->
              @for($day = 0; $day < 7; $day++)
              <td class="day-cell">
                <div class="status-buttons">
                  <button type="button" class="status-btn check-btn {{ ($task->week2_status[$day] ?? '') === '✓' ? 'active' : '' }}"
                          data-task-id="{{ $task->id }}" data-week="2" data-day="{{ $day }}" data-status="✓">✓</button>
                  <button type="button" class="status-btn wrong-btn {{ ($task->week2_status[$day] ?? '') === '✗' ? 'active' : '' }}"
                          data-task-id="{{ $task->id }}" data-week="2" data-day="{{ $day }}" data-status="✗">✗</button>
                </div>
              </td>
              @endfor

              <!-- Week 2 Remarks -->
              <td class="remarks-cell">
                <textarea class="remarks-input" data-task-id="{{ $task->id }}" data-week="2">{{ $task->week2_remarks ?? '' }}</textarea>
              </td>
            </tr>
            @endforeach

              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
body {
  font-family: Arial, sans-serif;
}

.table {
  border-collapse: collapse !important;
  margin: 0 !important;
}

.table td, .table th {
  border: 1px solid #000 !important;
  padding: 2px !important;
  vertical-align: middle !important;
  line-height: 1.2 !important;
}

.status-buttons {
  display: flex !important;
  gap: 2px !important;
  justify-content: center !important;
  align-items: center !important;
}

.status-btn {
  width: 14px !important;
  height: 14px !important;
  border: 1px solid #ccc !important;
  background: white !important;
  font-size: 8px !important;
  font-weight: bold !important;
  cursor: pointer !important;
  border-radius: 1px !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 1 !important;
}

.check-btn {
  color: #28a745 !important;
  border-color: #28a745 !important;
}

.check-btn:hover {
  background: #e8f5e8 !important;
}

.check-btn.active {
  background: #28a745 !important;
  color: white !important;
}

.wrong-btn {
  color: #dc3545 !important;
  border-color: #dc3545 !important;
}

.wrong-btn:hover {
  background: #fdeaea !important;
}

.wrong-btn.active {
  background: #dc3545 !important;
  color: white !important;
}

.remarks-input {
  border: none !important;
  resize: none !important;
  background: transparent !important;
  padding: 1px !important;
  margin: 0 !important;
  outline: none !important;
  width: 100% !important;
  height: 16px !important;
  font-size: 8px !important;
  line-height: 1.2 !important;
}

.remarks-input:focus {
  box-shadow: none !important;
  border: none !important;
}



/* Task description styling */
.task-cell {
  background-color: #f8f9fa !important;
  padding: 2px 6px !important;
  border: 1px solid #000 !important;
  font-size: 10px !important;
  line-height: 1.2 !important;
  vertical-align: middle !important;
}

/* Day cells */
.day-cell {
  text-align: center !important;
  padding: 1px !important;
  border: 1px solid #000 !important;
  width: 30px !important;
  height: 22px !important;
  vertical-align: middle !important;
}

/* Remarks cells */
.remarks-cell {
  padding: 1px !important;
  border: 1px solid #000 !important;
  width: 60px !important;
  vertical-align: middle !important;
}

@media print {
  .container-fluid {
    max-width: none !important;
    padding: 0 !important;
  }

  .table td, .table th {
    border: 1px solid #000 !important;
    -webkit-print-color-adjust: exact !important;
  }

  .category-cell {
    background-color: #28a745 !important;
    -webkit-print-color-adjust: exact !important;
  }

  .task-cell {
    background-color: #f8f9fa !important;
    -webkit-print-color-adjust: exact !important;
  }
}
</style>

<script>
// Update task status with buttons
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('status-btn')) {
        const taskId = e.target.dataset.taskId;
        const week = e.target.dataset.week;
        const day = e.target.dataset.day;
        const status = e.target.dataset.status;

        // Find other button in same cell and deactivate it
        const otherButtons = e.target.parentElement.querySelectorAll('.status-btn');
        otherButtons.forEach(btn => {
            if (btn !== e.target) {
                btn.classList.remove('active');
            }
        });

        // Toggle current button
        if (e.target.classList.contains('active')) {
            e.target.classList.remove('active');
            // Send empty status to clear
            updateTaskStatus(taskId, week, day, '');
        } else {
            e.target.classList.add('active');
            // Send status
            updateTaskStatus(taskId, week, day, status);
        }
    }
});

function updateTaskStatus(taskId, week, day, status) {
    fetch('{{ route("task.updateStatus") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            task_id: taskId,
            week: week,
            day: day,
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            alert('Error updating status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating status');
    });
}

// Update remarks with debounce
let remarksTimeout;
document.addEventListener('input', function(e) {
    if (e.target.classList.contains('remarks-input')) {
        clearTimeout(remarksTimeout);
        remarksTimeout = setTimeout(() => {
            const taskId = e.target.dataset.taskId;
            const week = e.target.dataset.week;
            const remarks = e.target.value;

            fetch('{{ route("task.updateRemarks") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    task_id: taskId,
                    week: week,
                    remarks: remarks
                })
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    alert('Error updating remarks');
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }, 1000); // Wait 1 second after user stops typing
    }
});

// Update week dates
function updateWeekDates() {
    const week1Date = document.getElementById('week1_date').value;
    const week2Date = document.getElementById('week2_date').value;

    if (week1Date) {
        fetch('{{ route("task.updateDates") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                week_start_date: week1Date
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Optionally reload the page to reflect new dates
                // location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }
}
</script>
@endsection
