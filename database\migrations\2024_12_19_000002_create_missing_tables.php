<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create category_limits table if it doesn't exist
        if (!Schema::hasTable('category_limits')) {
            Schema::create('category_limits', function (Blueprint $table) {
                $table->id();
                $table->string('category_name');
                $table->integer('max_total')->nullable();
                $table->integer('max_boys')->nullable();
                $table->integer('max_girls')->nullable();
                $table->timestamps();
            });
        }

        // Create task_checklists table if it doesn't exist
        if (!Schema::hasTable('task_checklists')) {
            Schema::create('task_checklists', function (Blueprint $table) {
                $table->id();
                $table->string('task_area');
                $table->text('task_description');
                $table->date('date');
                $table->enum('day_of_week', ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN']);
                $table->enum('status', ['pending', 'completed', 'failed'])->default('pending');
                $table->text('remarks')->nullable();
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('task_checklists');
        Schema::dropIfExists('category_limits');
    }
};
