<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
  <title>General Task Assignment Dashboard</title>

  <!-- External CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">

  <style>
    /* ===== BASE STYLES ===== */
    body {
      background: #eef2f7;
      font-family: "Poppins", sans-serif;
      margin: 0;
      padding: 0;
    }

    .row {
      font-family: 'Poppins', sans-serif;
      font-size: 20px;
      font-weight: 450;
    }

    /* ===== HEADER STYLES ===== */
    header {
      background-color: #22BBEA;
      color: white;
      padding: 25px;
      text-align: center;
    }

    .header-title {
      color: white !important;
      font-weight: 700;
      font-size: 2.5rem;
      margin-bottom: 20px;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .logo {
      margin-left: none;
    }

    .logo img {
      width: 500px;
      height: auto;
      margin-left: none;
    }

    /* ===== NAVIGATION STYLES ===== */
    .nav-buttons {
      margin-top: 15px;
    }

    .nav-btn {
      background-color: rgba(255,255,255,0.2);
      color: white !important;
      border: 2px solid rgba(255,255,255,0.3);
      padding: 10px 25px;
      border-radius: 25px;
      font-weight: 600;
      text-decoration: none;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }

    .nav-btn:hover {
      background-color: rgba(255,255,255,0.3);
      border-color: rgba(255,255,255,0.6);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      color: white !important;
    }

    .nav-btn.active {
      background-color: white;
      color: #22BBEA !important;
      border-color: white;
      font-weight: 700;
    }

    .nav-btn.active:hover {
      background-color: #f8f9fa;
      color: #22BBEA !important;
    }

    /* ===== SIDEBAR STYLES ===== */
    .sidebar {
      border-right: 3px solid #22BBEA;
    }

    .sidebar-icon {
      width: 30px;
      height: 30px;
      margin-right: 8px;
      vertical-align: middle;
    }

    .sidebar h3 {
      margin-top: 0;
    }

    .sidebar ul {
      list-style: none;
      padding-left: 0;
    }

    .sidebar ul li {
      margin: 15px 0;
    }

    .sidebar ul li a {
      text-decoration: none;
      color: black;
    }

    .sidebar ul li:hover {
      background: #fa5408;
      color: white;
      max-width: 100%;
      border-radius: 5px;
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    .sidebar ul li:hover a {
      color: white;
      transition: color 0.3s ease;
    }

    /* ===== CONTENT STYLES ===== */
    .content {
      flex: 1;
      padding: 30px;
      font-display: center;
    }
    .dashboard-title {
      font-weight: 700;
      color: #333;
    }
    .category-card {
      position: relative;
      border: none;
      border-radius: 20px;
      padding: 25px;
      transition: all 0.4s ease;
      color: #333;
      box-shadow: 0 8px 25px rgba(0,0,0,0.08);
      cursor: pointer;
      user-select: none;
      max-height: 300px;
      overflow-y: auto;
      backdrop-filter: blur(10px);
    }

    /* Individual Pastel Colors for Each Category */
    .category-card:nth-child(1) {
      background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 50%, #f48fb1 100%);
      border: 2px solid #f8bbd9;
    }

    .category-card:nth-child(2) {
      background: linear-gradient(135deg, #e3f2fd 0%, #90caf9 50%, #64b5f6 100%);
      border: 2px solid #90caf9;
    }

    .category-card:nth-child(3) {
      background: linear-gradient(135deg, #e8f5e8 0%, #a5d6a7 50%, #81c784 100%);
      border: 2px solid #a5d6a7;
    }

    .category-card:nth-child(4) {
      background: linear-gradient(135deg, #fff8e1 0%, #ffcc02 50%, #ffc107 100%);
      border: 2px solid #ffcc02;
    }

    .category-card:nth-child(5) {
      background: linear-gradient(135deg, #f3e5f5 0%, #ce93d8 50%, #ba68c8 100%);
      border: 2px solid #ce93d8;
    }

    .category-card:nth-child(6) {
      background: linear-gradient(135deg, #fff3e0 0%, #ffab91 50%, #ff8a65 100%);
      border: 2px solid #ffab91;
    }
    .category-label {
      font-size: 22px;
      font-weight: 800;
      color: #2c3e50;
      margin-bottom: 15px;
      padding-bottom: 8px;
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
      position: relative;
    }

    .category-label::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 3px;
      background: linear-gradient(90deg, rgba(255,255,255,0.8), rgba(255,255,255,0.4), rgba(255,255,255,0.8));
      border-radius: 2px;
    }
    .task-list {
      list-style-type: none;
      padding-left: 0;
      margin-bottom: 0;
      font-size: 14px;
      max-height: 180px;
      overflow-y: auto;
    }
    .task-list li {
      padding: 3px 0;
      border-bottom: 1px solid #ccc3;
    }
    .btn-custom {
      border-radius: 30px;
      padding: 8px 20px;
      font-weight: 500;
    }
    .table-container {
      background: white;
      padding: 20px;
      border-radius: 15px;
      box-shadow: 0 0 15px rgba(0,0,0,0.05);
    }
    .student-row td {
      vertical-align: middle;
    }
    .category-card:hover {
      transform: translateY(-12px) scale(1.03);
      box-shadow: 0 15px 40px rgba(0,0,0,0.2);
    }

    /* Enhanced hover effects for each category */
    .category-card:nth-child(1):hover {
      box-shadow: 0 15px 40px rgba(244, 143, 177, 0.4);
    }

    .category-card:nth-child(2):hover {
      box-shadow: 0 15px 40px rgba(100, 181, 246, 0.4);
    }

    .category-card:nth-child(3):hover {
      box-shadow: 0 15px 40px rgba(129, 199, 132, 0.4);
    }

    .category-card:nth-child(4):hover {
      box-shadow: 0 15px 40px rgba(255, 204, 2, 0.4);
    }

    .category-card:nth-child(5):hover {
      box-shadow: 0 15px 40px rgba(186, 104, 200, 0.4);
    }

    .category-card:nth-child(6):hover {
      box-shadow: 0 15px 40px rgba(255, 171, 145, 0.4);
    }
    nav.navbar-custom {
      background-color: #0d6efd;
      color: white;
    }
    nav.navbar-custom .navbar-brand,
    nav.navbar-custom .nav-link {
      color: white;
      font-weight: 600;
    }
    nav.navbar-custom .nav-link:hover {
      color: #ffd43b;
    }

    /* Centered Form Modal */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.4);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }
    .modal-content {
      background: white;
      padding: 25px;
      border-radius: 15px;
      width: 1100px;
      max-width: 90vw;
      margin: 0 auto;
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
      position: relative;
      left: 50%;
      transform: translateX(-50%);
    }
    .close-btn {
      position: absolute;
      top: 12px;
      right: 15px;
      background: #dc3545;
      border: none;
      color: white;
      font-weight: 700;
      font-size: 18px;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      line-height: 22px;
      cursor: pointer;
      user-select: none;
      transition: background-color 0.3s ease;
    }
    .close-btn:hover {
      background: #a71d2a;
    }

    /* Student Assignment Modal */
    #studentAssignModal {
      display: none;
      align-items: center;
      justify-content: center;
    }
    #studentAssignModal .modal-content {
      width: 1100px !important;
      max-width: 98vw;
      min-height: 420px;
      transition: width 0.3s, min-height 0.3s;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: stretch;
      font-size: 1.15rem;
    }
    #studentAssignModal .modal-content .fw-bold {
      font-size: 1.3rem;
    }
    #studentAssignModal .modal-content .coordinator-label {
      font-weight: 500;
      margin-bottom: 8px;
      font-size: 1.1rem;
      color: #444;
      text-align: left;
    }
    #studentAssignModal .modal-content .coordinator-name {
      font-weight: 400;
      font-size: 1.15rem;
      margin-left: 3px;
      color: #222;
    }
    #studentAssignModal .modal-content ul.list-group-flush li {
      font-size: 1.08rem;
      padding: 8px 0;
    }

    /* Task Assignment History Modal */
    #historyModal.show {
      display: flex !important;
      align-items: center;
      justify-content: center;
    }
    #historyModal .modal-dialog {
      margin: 1.75rem auto;
      max-width: 95%;
      width: 95%;
    }
    #historyModal .modal-content {
      width: 100%;
      max-width: 1800px;
      position: relative;
      margin: 0 auto;
    }

    /* Add spacing to history modal table cells */
    #historyModal .table-bordered td {
      padding: 15px !important;
    }

    #historyModal .table-sm td {
      padding: 8px 12px !important;
      border: 1px solid #dee2e6 !important;
    }

    #historyModal .table-sm th {
      padding: 10px 12px !important;
      border: 1px solid #dee2e6 !important;
    }

    /* Timeline Styles */
    .timeline-item {
      position: relative;
      padding-left: 30px;
      margin-bottom: 20px;
    }
    .timeline-item .dot {
      position: absolute;
      left: 0;
      top: 5px;
      width: 10px;
      height: 10px;
      border-radius: 50%;
    }
    .dot { width: 12px; height: 12px; border-radius: 50%; display: inline-block; }
    .dot-blue { background: #0d6efd; }
    .dot-pink { background: #e83e8c; }
    .dot-green { background: #28a745; }
    .timeline-item { background: #f8f9fa; border-radius: 10px; padding: 12px 16px; }

    /* Custom container width for maximum space utilization */
    .container-fluid {
      --bs-gutter-x: 1rem;
      --bs-gutter-y: 0;
      max-width: 100vw;
      width: 100%;
      padding-right: calc(var(--bs-gutter-x) * .5);
      padding-left: calc(var(--bs-gutter-x) * .5);
      margin: 0;
    }

    /* Optimize main content spacing */
    main.col-md-10 {
      padding: 15px !important;
      margin: 0;
    }

    /* Optimize row and column spacing */
    .row {
      --bs-gutter-x: 1rem;
      --bs-gutter-y: 1rem;
      margin: 0;
    }

    .col-lg-4, .col-md-6 {
      padding-left: 8px;
      padding-right: 8px;
    }

    /* Reduce unnecessary margins */
    .dashboard-title {
      margin-bottom: 10px !important;
    }

    .text-muted.mb-4 {
      margin-bottom: 20px !important;
    }

    /* Edit Button with Blue Line Styling */
    .btn-edit-blue {
      background: linear-gradient(135deg, #007bff, #0056b3);
      border: 2px solid #007bff;
      color: white;
      padding: 8px 20px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .btn-edit-blue:before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s;
    }

    .btn-edit-blue:hover {
      background: linear-gradient(135deg, #0056b3, #004085);
      border-color: #0056b3;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }

    .btn-edit-blue:hover:before {
      left: 100%;
    }

    .btn-edit-blue:active {
      transform: translateY(0);
      box-shadow: 0 2px 6px rgba(0, 123, 255, 0.2);
    }

    /* Coordinator highlighting styles for View Members modal */
    .coordinator-highlight-cell {
      background-color: #f5e6a3 !important;
      border: 1px solid #d4c69a !important;
      padding: 8px !important;
    }

    .coordinator-name {
      font-weight: 600 !important;
      color: #856404 !important;
    }

    /* Coordinator highlighting for View History rows */
    .coordinator-highlight-row {
      background-color: #fff3cd !important;
      border: 1px solid #ffeaa7 !important;
      padding: 4px 8px !important;
      border-radius: 4px !important;
      margin-bottom: 2px !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }



    /* Make all member names in View History single line */
    .modal-body .row .col div {
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      line-height: 1.4 !important;
      margin-bottom: 2px !important;
    }

    /* Coordinator highlighting for Edit Members modal */
    .coordinator-highlight-edit {
      background-color: #f5e6a3 !important;
      border: 1px solid #d4c69a !important;
      border-radius: 6px !important;
    }

    .coordinator-highlight-edit:hover {
      border-color: #ff9800 !important;
      box-shadow: 0 2px 8px rgba(255, 193, 7, 0.5) !important;
    }

    /* Coordinator highlighting styles */
    .coordinator-highlight-cell {
      background-color: #f5e6a3 !important;
      border: 1px solid #d4c69a !important;
    }

    .coordinator-name {
      font-weight: 600 !important;
      color: #856404 !important;
    }

    /* Edit Member Styles */
    .member-list-container {
      max-height: 400px;
      overflow-y: auto;
      padding-right: 10px;
    }

    .member-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 15px;
      margin-bottom: 8px;
      background-color: #ffffff;
      border-radius: 6px;
      border: 1px solid #e0e0e0;
      transition: all 0.2s ease;
    }

    .member-row:hover {
      border-color: #007bff;
      box-shadow: 0 1px 4px rgba(0,123,255,0.15);
    }

    .member-info {
      flex: 1;
    }

    .member-name {
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
      font-size: 0.95em;
    }

    .member-comment {
      font-size: 0.85em;
      color: #666;
      font-style: italic;
      margin-top: 4px;
    }

    .comment-edit-area {
      margin-top: 8px;
    }

    .comment-input {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 0.9em;
    }

    .member-actions {
      display: flex;
      gap: 8px;
      margin-left: 15px;
    }

    .btn-edit-small, .btn-save-small {
      padding: 6px 16px;
      font-size: 0.8em;
      border-radius: 4px;
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;
      font-weight: 500;
      min-width: 50px;
    }

    .btn-edit-small {
      background: #17a2b8;
      color: white;
    }

    .btn-edit-small:hover {
      background: #138496;
    }

    .btn-save-small {
      background: #28a745;
      color: white;
    }

    .btn-save-small:hover {
      background: #1e7e34;
    }

    .btn-save-small:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    .comment-input {
      width: 100%;
      padding: 4px 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 12px;
      margin-top: 4px;
    }

    .member-comment {
      font-size: 11px;
      color: #666;
      font-style: italic;
      margin-top: 2px;
    }

    /* ===== PASTEL TASK CARD COLORS ===== */
    :root {
      --pastel-pink: #fce4ec;
      --pastel-blue: #e3f2fd;
      --pastel-green: #e8f5e8;
      --pastel-yellow: #fff8e1;
      --pastel-purple: #f3e5f5;
      --pastel-orange: #fff3e0;
      --pastel-teal: #e0f2f1;
      --pastel-lavender: #f8f4ff;
      --pastel-peach: #ffeee6;
      --pastel-mint: #e8f8f5;
      --pastel-coral: #ffe8e8;
      --pastel-sky: #e6f3ff;
    }

    /* Task Card Pastel Styling - Soft & Easy on Eyes */
    .col-lg-4:nth-child(1) .category-card > div {
      background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%) !important;
      border: 1px solid #f8bbd9 !important;
      box-shadow: 0 4px 15px rgba(248, 187, 217, 0.2) !important;
      transition: all 0.3s ease !important;
    }

    .col-lg-4:nth-child(1) .category-card > div:hover {
      transform: translateY(-3px) !important;
      box-shadow: 0 8px 25px rgba(248, 187, 217, 0.3) !important;
    }

    .col-lg-4:nth-child(1) .category-label {
      color: #ad1457 !important;
    }

    .col-lg-4:nth-child(2) .category-card > div {
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
      border: 1px solid #bbdefb !important;
      box-shadow: 0 4px 15px rgba(187, 222, 251, 0.2) !important;
      transition: all 0.3s ease !important;
    }

    .col-lg-4:nth-child(2) .category-card > div:hover {
      transform: translateY(-3px) !important;
      box-shadow: 0 8px 25px rgba(187, 222, 251, 0.3) !important;
    }

    .col-lg-4:nth-child(2) .category-label {
      color: #1565c0 !important;
    }

    .col-lg-4:nth-child(3) .category-card > div {
      background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%) !important;
      border: 1px solid #c8e6c9 !important;
      box-shadow: 0 4px 15px rgba(200, 230, 201, 0.2) !important;
      transition: all 0.3s ease !important;
    }

    .col-lg-4:nth-child(3) .category-card > div:hover {
      transform: translateY(-3px) !important;
      box-shadow: 0 8px 25px rgba(200, 230, 201, 0.3) !important;
    }

    .col-lg-4:nth-child(3) .category-label {
      color: #2e7d32 !important;
    }

    .col-lg-4:nth-child(4) .category-card > div {
      background: linear-gradient(135deg, #fff8e1 0%, #fff3c4 100%) !important;
      border: 1px solid #fff3c4 !important;
      box-shadow: 0 4px 15px rgba(255, 243, 196, 0.2) !important;
      transition: all 0.3s ease !important;
    }

    .col-lg-4:nth-child(4) .category-card > div:hover {
      transform: translateY(-3px) !important;
      box-shadow: 0 8px 25px rgba(255, 243, 196, 0.3) !important;
    }

    .col-lg-4:nth-child(4) .category-label {
      color: #f57c00 !important;
    }

    .col-lg-4:nth-child(5) .category-card > div {
      background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%) !important;
      border: 1px solid #e1bee7 !important;
      box-shadow: 0 4px 15px rgba(225, 190, 231, 0.2) !important;
      transition: all 0.3s ease !important;
    }

    .col-lg-4:nth-child(5) .category-card > div:hover {
      transform: translateY(-3px) !important;
      box-shadow: 0 8px 25px rgba(225, 190, 231, 0.3) !important;
    }

    .col-lg-4:nth-child(5) .category-label {
      color: #6a1b9a !important;
    }

    .col-lg-4:nth-child(6) .category-card > div {
      background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%) !important;
      border: 1px solid #ffcc80 !important;
      box-shadow: 0 4px 15px rgba(255, 204, 128, 0.2) !important;
      transition: all 0.3s ease !important;
    }

    .col-lg-4:nth-child(6) .category-card > div:hover {
      transform: translateY(-3px) !important;
      box-shadow: 0 8px 25px rgba(255, 204, 128, 0.3) !important;
    }

    .col-lg-4:nth-child(6) .category-label {
      color: #ef6c00 !important;
    }

    /* Enhanced badge styling for pastel cards */
    .category-card .badge {
      border-radius: 20px !important;
      padding: 8px 15px !important;
      font-size: 14px !important;
      font-weight: 700 !important;
      margin: 0 5px !important;
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1) !important;
    }

    /* Enhanced button styling for pastel cards */
    .category-card .btn {
      border-radius: 25px !important;
      padding: 10px 20px !important;
      font-weight: 600 !important;
      transition: all 0.3s ease !important;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
    }

    .category-card .btn:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
    }


  </style>
</head>
<body>
    <header>
    <div class="logo">
      <img src="<?php echo e(asset('images/pnlogo-header.png')); ?>" alt="PN Logo">
    </div>
  </header>
  <div class="container-fluid">
    <div class="row">
      <!-- Sidebar -->
      <nav class="col-md-2 d-none d-md-block sidebar bg-light">
        <ul class="nav flex-column">
         <li class="nav-item"><a href="<?php echo e(route('dashboard')); ?>" class="nav-link sidebar-link">
           <img src="<?php echo e(asset('images/dashboard.png')); ?>" class="sidebar-icon">Dashboard </a>
        </li>

        <li class="nav-item"><a href="<?php echo e(route('students.index')); ?>" class="nav-link">
          <i class="bi bi-people sidebar-icon"></i>Students</a>
        </li>
        <li class="nav-item"><a href="<?php echo e(route('batches.index')); ?>" class="nav-link">
          <i class="bi bi-calendar3 sidebar-icon"></i>Batches</a>
        </li>
        <li class="nav-item"><a href="#" class="nav-link">
          <img src="<?php echo e(asset('images/assign.png')); ?>" class="sidebar-icon">General Tasks</a>
        </li>
          <li class="nav-item"><a href="" class="nav-link">Room Task History</a></li>
          <li class="nav-item"><a href="#" class="nav-link">Reports</a></li>
          <li class="nav-item"><a href="#" class="nav-link">Log Out</a></li>
        </ul>
      </nav>
      <!-- Main content beside sidebar, centered -->
      <main class="col-md-10" style="min-height: 90vh; padding: 20px;">
        <div class="container-fluid py-3">
          <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
              <h2 class="dashboard-title mb-2">General Task Assignments</h2>
              <p class="text-muted mb-0">Manage and track all task assignments across categories</p>
            </div>
            <div class="d-flex gap-2">
              <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#taskChecklistModal">
                <i class="bi bi-list-task me-2"></i>Task Description
              </button>
              <a href="<?php echo e(route('batches.index')); ?>" class="btn btn-primary">
                <i class="bi bi-calendar3 me-2"></i>Manage Batches
              </a>
            </div>
          </div>

          <!-- Task Descriptions Table -->
          <div class="card mb-4">
            <div class="card-header bg-primary text-white">Task Categories & Descriptions</div>
            <div class="card-body p-0">
              <table class="table table-bordered mb-0">
                <thead>
                  <tr>
                    <th>Task Category</th>
                    <th>Description</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Kitchen</td>
                    <td>Responsible for food preparation, cooking, and maintaining cleanliness in the kitchen area.</td>
                  </tr>
                  <tr>
                    <td>Dishwashing</td>
                    <td>Handles washing, drying, and organizing all kitchen utensils, plates, and cookware.</td>
                  </tr>
                  <tr>
                    <td>Dining</td>
                    <td>Prepares the dining area, sets tables, serves food, and ensures the dining space is clean.</td>
                  </tr>
                  <tr>
                    <td>Offices & Conference Rooms</td>
                    <td>Cleans and organizes offices and conference rooms, including dusting, sweeping, and arranging furniture.</td>
                  </tr>
                  <tr>
                    <td>Garbage, Rugs, & Rooftop</td>
                    <td>Manages waste disposal, cleans rugs, and maintains the cleanliness of the rooftop area.</td>
                  </tr>
                  <tr>
                    <td>Ground Floor</td>
                    <td>Ensures the ground floor is tidy, including sweeping, mopping, and general maintenance.</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div class="row" id="category-cards-container">
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <div class="col-lg-4 col-md-6">
                <div class="category-card text-center p-0 overflow-hidden" style="background:none; border:none; box-shadow:none;">
                  <div style="height:100%; min-height:100%; border-radius:15px; padding:20px; background:#f8f9fa; box-shadow: 0 0 10px rgba(0,0,0,0.07); position: relative;">
                    <div class="category-label" style="background:none; border:none; margin-bottom:10px; font-size:20px; font-weight:700; color:#333;">
                      <?php echo e($category->name); ?>

                    </div>
                    <div class="mb-2">
                      <?php
                        $boys = 0; $girls = 0;
                        $coor2025 = null; $coor2026 = null;
                        foreach($category->assignments as $assignment) {
                          foreach($assignment->assignmentMembers as $member) {
                            if($member->student->gender === 'Male') $boys++;
                            if($member->student->gender === 'Female') $girls++;
                            if($member->is_coordinator && $member->student->batch == 2025 && !$coor2025) $coor2025 = $member->student->name;
                            if($member->is_coordinator && $member->student->batch == 2026 && !$coor2026) $coor2026 = $member->student->name;
                          }
                        }
                      ?>
                      <span class="badge" style="background:#1565c0; color:#fff; font-weight:600;">Boys: <?php echo e($boys); ?></span>
                      <span class="badge" style="background:#f3f6fb; color:#222; font-weight:600;">Girls: <?php echo e($girls); ?></span>
                    </div>
                    <div class="mb-2">
                      <div><b>Coordinator 2025:</b> <?php echo e($coor2025 ?? 'None Assigned'); ?></div>
                      <div><b>Coordinator 2026:</b> <?php echo e($coor2026 ?? 'None Assigned'); ?></div>
                    </div>
                    <button class="btn btn-outline-primary btn-sm mt-2" data-bs-toggle="modal" data-bs-target="#studentAssignModal<?php echo e($category->id); ?>">View Members</button>
                  </div>
                </div>
              </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          </div>

          <div class="d-flex justify-content-start align-items-center gap-2 mb-4" style="margin-top: 30px;">
            <a href="<?php echo e(url('/assignments/create')); ?>" class="btn btn-primary btn-custom">+ Assign New Tasks</a>
            <a href="<?php echo e(url('/assignments')); ?>" class="btn btn-success btn-custom">View All Assignments</a>
            <form method="POST" action="<?php echo e(url('/assignments/auto-shuffle')); ?>" style="display:inline;">
              <?php echo csrf_field(); ?>
              <button type="submit" class="btn btn-warning btn-custom">Auto-Shuffle</button>
            </form>
            <button class="btn btn-outline-dark btn-custom" data-bs-toggle="modal" data-bs-target="#historyModal">View History</button>
          </div>

    <div id="task-table-container" class="table-container">
      <table class="table table-hover">
        <thead class="table-light">
          <tr>
            <th>STUDENT COOR</th>
            <th>CLASS YEAR</th>
            <th>STUDENT COOR</th>
            <th>CLASS YEAR</th>
            <th>CATEGORY</th>
            <th>DATE ASSIGN</th>
          </tr>
        </thead>
        <tbody id="task-table-body">
          <?php $__currentLoopData = $assignments->where('status', 'current')->unique('category_id'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $assignment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
              $coor2025 = $assignment->assignmentMembers->where('is_coordinator', true)->where('student.batch', 2025)->first();
              $coor2026 = $assignment->assignmentMembers->where('is_coordinator', true)->where('student.batch', 2026)->first();
            ?>
            <tr>
              <td><?php echo e($coor2025 ? $coor2025->student->name : 'N/A'); ?></td>
              <td><?php echo e($coor2025 ? $coor2025->student->batch : 'N/A'); ?></td>
              <td><?php echo e($coor2026 ? $coor2026->student->name : 'N/A'); ?></td>
              <td><?php echo e($coor2026 ? $coor2026->student->batch : 'N/A'); ?></td>
              <td><?php echo e($assignment->category->name); ?></td>
              <td><?php echo e($assignment->start_date); ?> - <?php echo e($assignment->end_date); ?></td>
            </tr>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
      </table>
    </div>
        </div>
        </main>
      </div>
    </div>

  <!-- Student Assignment Modals for each category -->
  <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
  <div class="modal fade" id="studentAssignModal<?php echo e($category->id); ?>" tabindex="-1" aria-labelledby="studentAssignModalLabel<?php echo e($category->id); ?>" aria-hidden="true">
    <div class="modal-dialog" style="max-width:1200px; width:85vw; margin: 1.75rem auto;">
      <div class="modal-content" style="width: 100%; margin: 0 auto; transform: none; left: auto;">
        <div class="modal-header">
          <h5 class="modal-title" id="studentAssignModalLabel<?php echo e($category->id); ?>" style="font-size: 22px;">Members for <?php echo e($category->name); ?></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <table class="table table-bordered" style="font-size: 18px;">
            <thead>
              <tr>
                <?php $__currentLoopData = $activeBatches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $batch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <th style="font-size: 20px; padding: 15px; text-align: center; background-color: #f8f9fa;"><?php echo e($batch->display_name); ?></th>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </tr>
            </thead>
            <tbody>
              <?php
                $membersByBatch = [];
                foreach($activeBatches as $batch) {
                  $membersByBatch[$batch->year] = [];
                }

                foreach($category->assignments as $assignment) {
                  foreach($assignment->assignmentMembers as $member) {
                    if(isset($membersByBatch[$member->student->batch])) {
                      $membersByBatch[$member->student->batch][] = $member;
                    }
                  }
                }

                $maxRows = 0;
                foreach($membersByBatch as $members) {
                  $maxRows = max($maxRows, count($members));
                }
              ?>
              <?php for($i = 0; $i < $maxRows; $i++): ?>
                <tr>
                  <?php $__currentLoopData = $activeBatches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $batch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php $members = $membersByBatch[$batch->year] ?? []; ?>
                    <td class="<?php echo e(isset($members[$i]) && $members[$i]->is_coordinator ? 'coordinator-highlight-cell' : ''); ?>" style="padding: 15px; font-size: 18px;">
                      <?php if(isset($members[$i])): ?>
                        <span class="<?php echo e($members[$i]->is_coordinator ? 'coordinator-name' : ''); ?>" style="font-size: 18px;"><?php echo e($members[$i]->student->name); ?></span>
                        <?php if($members[$i]->comments): ?>
                          <span class="text-muted" style="font-size: 16px;"> (<?php echo e($members[$i]->comments); ?>)</span>
                        <?php endif; ?>
                      <?php endif; ?>
                    </td>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tr>
              <?php endfor; ?>
            </tbody>
          </table>

          <!-- Add and Delete Buttons - Above Edit Button -->
          <div class="text-center mt-3 mb-2">
            <button type="button" class="btn btn-success me-2" onclick="openAddMembersModal(<?php echo e($category->id); ?>, '<?php echo e($category->name); ?>')">
              <i class="bi bi-person-plus me-1"></i>Add
            </button>
            <button type="button" class="btn btn-danger" onclick="openDeleteMembersModal(<?php echo e($category->id); ?>, '<?php echo e($category->name); ?>')">
              <i class="bi bi-person-dash me-1"></i>Delete
            </button>
          </div>

          <!-- Edit Button - Below Add/Delete buttons -->
          <div class="text-center">
            <button type="button" class="btn btn-edit-blue" onclick="openEditMembersModal(<?php echo e($category->id); ?>, '<?php echo e($category->name); ?>')">
              <i class="bi bi-pencil-square me-1"></i>Edit Members
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

  <!-- Add Members Modal -->
  <div class="modal fade" id="addMembersModal" tabindex="-1" aria-labelledby="addMembersModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="max-width:1200px; width:95vw;">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="addMembersModalLabel">Add Members to <span id="addCategoryName"></span></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <!-- Add New Student Section -->
          <div class="row mb-4">
            <div class="col-md-6">
              <div class="card border-success">
                <div class="card-header bg-success text-white">
                  <h6 class="mb-0">Add New Student - Batch 2025</h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-8">
                      <input type="text" class="form-control" id="newStudent2025Name" placeholder="Enter student name" style="font-size: 16px;">
                    </div>
                    <div class="col-md-4">
                      <select class="form-control" id="newStudent2025Gender" style="font-size: 16px;">
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                      </select>
                    </div>
                  </div>
                  <button type="button" class="btn btn-success btn-sm mt-2" onclick="addNewStudentToCategory(2025)">
                    <i class="bi bi-plus-circle"></i> Add to Category
                  </button>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card border-success">
                <div class="card-header bg-success text-white">
                  <h6 class="mb-0">Add New Student - Batch 2026</h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-8">
                      <input type="text" class="form-control" id="newStudent2026Name" placeholder="Enter student name" style="font-size: 16px;">
                    </div>
                    <div class="col-md-4">
                      <select class="form-control" id="newStudent2026Gender" style="font-size: 16px;">
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                      </select>
                    </div>
                  </div>
                  <button type="button" class="btn btn-success btn-sm mt-2" onclick="addNewStudentToCategory(2026)">
                    <i class="bi bi-plus-circle"></i> Add to Category
                  </button>
                </div>
              </div>
            </div>
          </div>

          <hr>

          <!-- Existing Students Section -->
          <div class="row">
            <div class="col-md-6">
              <h6 class="fw-bold mb-3 text-primary">Available Students - Batch 2025</h6>
              <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                <table class="table table-hover">
                  <tbody id="availableStudents2025">
                    <!-- Available students will be loaded here -->
                  </tbody>
                </table>
              </div>
            </div>
            <div class="col-md-6">
              <h6 class="fw-bold mb-3 text-primary">Available Students - Batch 2026</h6>
              <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                <table class="table table-hover">
                  <tbody id="availableStudents2026">
                    <!-- Available students will be loaded here -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <hr>

          <div class="row">
            <div class="col-12">
              <h6 class="fw-bold mb-3 text-primary">Selected Students to Add</h6>
              <div id="selectedStudentsToAdd" class="border rounded p-3" style="min-height: 100px; background-color: #f8f9fa;">
                <p class="text-muted mb-0">Click on students above to select them for adding to this category.</p>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-success" id="confirmAddMembers">Add Selected Members</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Delete Members Modal -->
  <div class="modal fade" id="deleteMembersModal" tabindex="-1" aria-labelledby="deleteMembersModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="max-width:1000px; width:90vw;">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="deleteMembersModalLabel">Delete Members from <span id="deleteCategoryName"></span></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <h6 class="fw-bold mb-3 text-warning">Current Members - Batch 2025</h6>
              <p class="small text-muted">Click to select for removal from category only</p>
              <div id="currentMembers2025" style="max-height: 300px; overflow-y: auto;">
                <!-- Current members will be loaded here -->
              </div>
            </div>
            <div class="col-md-6">
              <h6 class="fw-bold mb-3 text-warning">Current Members - Batch 2026</h6>
              <p class="small text-muted">Click to select for removal from category only</p>
              <div id="currentMembers2026" style="max-height: 300px; overflow-y: auto;">
                <!-- Current members will be loaded here -->
              </div>
            </div>
          </div>

          <hr>

          <div class="row">
            <div class="col-12">
              <h6 class="fw-bold mb-3 text-warning">Selected Members to Remove from Category</h6>
              <div id="selectedMembersToDelete" class="border rounded p-3" style="min-height: 80px; background-color: #fff3cd;">
                <p class="text-muted mb-0">Click on members above to select them for removal from this category.</p>
              </div>
            </div>
          </div>

          <hr>

          <!-- Delete from System Section -->
          <div class="row">
            <div class="col-12">
              <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                  <h6 class="mb-0"><i class="bi bi-exclamation-triangle"></i> Permanently Delete Students from System</h6>
                </div>
                <div class="card-body">
                  <p class="text-danger small mb-3">
                    <strong>Warning:</strong> This will permanently delete students from the entire system and all assignments.
                  </p>
                  <div class="row">
                    <div class="col-md-6">
                      <h6 class="text-danger">Batch 2025 Students</h6>
                      <div id="systemDeleteMembers2025" style="max-height: 200px; overflow-y: auto;">
                        <!-- Members for system deletion will be loaded here -->
                      </div>
                    </div>
                    <div class="col-md-6">
                      <h6 class="text-danger">Batch 2026 Students</h6>
                      <div id="systemDeleteMembers2026" style="max-height: 200px; overflow-y: auto;">
                        <!-- Members for system deletion will be loaded here -->
                      </div>
                    </div>
                  </div>
                  <div class="mt-3">
                    <h6 class="text-danger">Selected Students to Delete from System</h6>
                    <div id="selectedStudentsToDeleteFromSystem" class="border rounded p-3" style="min-height: 60px; background-color: #f8d7da;">
                      <p class="text-muted mb-0">Click on students above to select them for permanent deletion.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-warning" id="confirmDeleteMembers">Remove from Category</button>
          <button type="button" class="btn btn-danger" id="confirmDeleteFromSystem">Delete from System</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Edit Members Modal -->
  <div class="modal fade" id="editMembersModal" tabindex="-1" aria-labelledby="editMembersModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="max-width:900px; width:90vw;">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="editMembersModalLabel">Edit Members for <span id="editCategoryName"></span></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body" style="padding: 20px;">
          <div class="row">
            <div class="col-md-6">
              <h6 class="fw-bold mb-3 text-primary">Batch 2025</h6>
              <div id="editMembers2025Container" class="member-list-container">
                <!-- Members will be loaded here -->
              </div>
            </div>

            <div class="col-md-6">
              <h6 class="fw-bold mb-3 text-primary">Batch 2026</h6>
              <div id="editMembers2026Container" class="member-list-container">
                <!-- Members will be loaded here -->
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Task Assignment History Modal -->
  <div class="modal fade" id="historyModal" tabindex="-1" aria-labelledby="historyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="historyModalLabel">Task Assignment History</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div style="max-height:80vh; overflow-y:auto;">
            <table class="table table-bordered align-middle">
              <thead class="table-light">
                <tr>
                  <th style="width: 120px;">Category</th>
                  <th style="width: 400px;">Period & Members</th>
                  <th style="width: 400px;">Previous Assignment</th>
                  <th style="width: 80px;">Status</th>
                </tr>
              </thead>
              <tbody>
                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <?php
                    // Get current and previous assignments for this category
                    $catAssignments = $assignmentHistory->where('category_id', $category->id)->sortByDesc('id')->values();
                    $current = $catAssignments->where('status', 'current')->first();
                    $previous = $catAssignments->where('status', 'previous')->sortByDesc('id')->first();
                  ?>
                  <tr>
                    <td class="fw-bold align-top"><?php echo e($category->name); ?></td>
                    <td>
                      <?php if($current): ?>
                        <div>
                          <span class="fw-semibold text-primary">Period: <?php echo e($current->start_date); ?> - <?php echo e($current->end_date); ?></span>
                        </div>
                        <?php
                          $members2025 = $current->assignmentMembers->where('student.batch', 2025)->values();
                          $members2026 = $current->assignmentMembers->where('student.batch', 2026)->values();
                          $maxRows = max($members2025->count(), $members2026->count());
                        ?>
                        <table class="table table-bordered table-sm mt-2">
                          <thead class="table-light">
                            <tr>
                              <th class="fw-bold text-center" style="width: 50%;">Batch 2025</th>
                              <th class="fw-bold text-center" style="width: 50%;">Batch 2026</th>
                            </tr>
                          </thead>
                          <tbody>
                            <?php for($i = 0; $i < $maxRows; $i++): ?>
                              <tr>
                                <td class="<?php echo e(isset($members2025[$i]) && $members2025[$i]->is_coordinator ? 'coordinator-highlight-cell' : ''); ?>">
                                  <?php if(isset($members2025[$i])): ?>
                                    <span class="<?php echo e($members2025[$i]->is_coordinator ? 'coordinator-name' : ''); ?>"><?php echo e($members2025[$i]->student->name); ?></span>
                                    <?php if($members2025[$i]->comments): ?>
                                      <span class="text-muted small"> (<?php echo e($members2025[$i]->comments); ?>)</span>
                                    <?php endif; ?>
                                  <?php endif; ?>
                                </td>
                                <td class="<?php echo e(isset($members2026[$i]) && $members2026[$i]->is_coordinator ? 'coordinator-highlight-cell' : ''); ?>">
                                  <?php if(isset($members2026[$i])): ?>
                                    <span class="<?php echo e($members2026[$i]->is_coordinator ? 'coordinator-name' : ''); ?>"><?php echo e($members2026[$i]->student->name); ?></span>
                                    <?php if($members2026[$i]->comments): ?>
                                      <span class="text-muted small"> (<?php echo e($members2026[$i]->comments); ?>)</span>
                                    <?php endif; ?>
                                  <?php endif; ?>
                                </td>
                              </tr>
                            <?php endfor; ?>
                          </tbody>
                        </table>
                      <?php else: ?>
                        <span class="text-muted">No assignment</span>
                      <?php endif; ?>
                    </td>
                    <td>
                      <?php if($previous): ?>
                        <div>
                          <span class="fw-semibold text-primary">Period: <?php echo e($previous->start_date); ?> - <?php echo e($previous->end_date); ?></span>
                        </div>
                        <?php
                          $prevMembers2025 = $previous->assignmentMembers->where('student.batch', 2025)->values();
                          $prevMembers2026 = $previous->assignmentMembers->where('student.batch', 2026)->values();
                          $prevMaxRows = max($prevMembers2025->count(), $prevMembers2026->count());
                        ?>
                        <table class="table table-bordered table-sm mt-2">
                          <thead class="table-light">
                            <tr>
                              <th class="fw-bold text-center" style="width: 50%;">Batch 2025</th>
                              <th class="fw-bold text-center" style="width: 50%;">Batch 2026</th>
                            </tr>
                          </thead>
                          <tbody>
                            <?php for($i = 0; $i < $prevMaxRows; $i++): ?>
                              <tr>
                                <td class="<?php echo e(isset($prevMembers2025[$i]) && $prevMembers2025[$i]->is_coordinator ? 'coordinator-highlight-cell' : ''); ?>">
                                  <?php if(isset($prevMembers2025[$i])): ?>
                                    <span class="<?php echo e($prevMembers2025[$i]->is_coordinator ? 'coordinator-name' : ''); ?>"><?php echo e($prevMembers2025[$i]->student->name); ?></span>
                                    <?php if($prevMembers2025[$i]->comments): ?>
                                      <span class="text-muted small"> (<?php echo e($prevMembers2025[$i]->comments); ?>)</span>
                                    <?php endif; ?>
                                  <?php endif; ?>
                                </td>
                                <td class="<?php echo e(isset($prevMembers2026[$i]) && $prevMembers2026[$i]->is_coordinator ? 'coordinator-highlight-cell' : ''); ?>">
                                  <?php if(isset($prevMembers2026[$i])): ?>
                                    <span class="<?php echo e($prevMembers2026[$i]->is_coordinator ? 'coordinator-name' : ''); ?>"><?php echo e($prevMembers2026[$i]->student->name); ?></span>
                                    <?php if($prevMembers2026[$i]->comments): ?>
                                      <span class="text-muted small"> (<?php echo e($prevMembers2026[$i]->comments); ?>)</span>
                                    <?php endif; ?>
                                  <?php endif; ?>
                                </td>
                              </tr>
                            <?php endfor; ?>
                          </tbody>
                        </table>
                      <?php else: ?>
                        <span class="text-muted">No previous assignment</span>
                      <?php endif; ?>
                    </td>
                    <td class="text-center align-top">
                      <?php if($loop->first): ?>
                        <span class="badge bg-success">Current</span>
                      <?php endif; ?>
                    </td>
                  </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Task Checklist Modal -->
  <div class="modal fade" id="taskChecklistModal" tabindex="-1" aria-labelledby="taskChecklistModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="taskChecklistModalLabel">
            <i class="bi bi-list-check me-2"></i>Task Checklist
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body p-0">
          <!-- Navigation Controls -->
          <div class="d-flex justify-content-between align-items-center p-3 bg-light border-bottom">
            <button class="btn btn-outline-primary" onclick="navigateTaskPage('prev')">
              <i class="bi bi-arrow-left"></i> Previous
            </button>
            <span class="fw-bold">Page <span id="currentPageNumber">1</span> of 10</span>
            <button class="btn btn-outline-primary" onclick="navigateTaskPage('next')">
              Next <i class="bi bi-arrow-right"></i>
            </button>
          </div>

          <!-- Task Content Area -->
          <div id="taskPageContent" class="p-3">
            <!-- Content will be loaded dynamically -->
          </div>
        </div>
      </div>
    </div>






  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <script>
    function editCategoryMembers(categoryId, categoryName) {
      // You can customize this function based on your needs
      // For now, it will redirect to an edit page or show an edit modal

      // Option 1: Redirect to edit page
      window.location.href = `/assignments/category/${categoryId}/edit`;

      // Option 2: Show alert (for testing)
      // alert(`Edit members for ${categoryName} (Category ID: ${categoryId})`);

      // Option 3: Open edit modal (you would need to create this modal)
      // $('#editCategoryModal').modal('show');
      // populateEditModal(categoryId, categoryName);
    }

    // Global variables for add/delete functionality
    let currentCategoryId = null;
    let selectedStudentsToAdd = [];
    let selectedMembersToDelete = [];
    let selectedStudentsToDeleteFromSystem = [];

    // Open Add Members Modal
    function openAddMembersModal(categoryId, categoryName) {
      currentCategoryId = categoryId;
      selectedStudentsToAdd = [];

      // Set category name
      document.getElementById('addCategoryName').textContent = categoryName;

      // Clear selected students display
      document.getElementById('selectedStudentsToAdd').innerHTML = '<p class="text-muted mb-0">Click on students above to select them for adding to this category.</p>';

      // Load available students
      loadAvailableStudents(categoryId);

      // Show modal
      const addModal = new bootstrap.Modal(document.getElementById('addMembersModal'));
      addModal.show();
    }

    // Open Delete Members Modal
    function openDeleteMembersModal(categoryId, categoryName) {
      currentCategoryId = categoryId;
      selectedMembersToDelete = [];
      selectedStudentsToDeleteFromSystem = [];

      // Set category name
      document.getElementById('deleteCategoryName').textContent = categoryName;

      // Clear selected displays
      document.getElementById('selectedMembersToDelete').innerHTML = '<p class="text-muted mb-0">Click on members above to select them for removal from this category.</p>';
      document.getElementById('selectedStudentsToDeleteFromSystem').innerHTML = '<p class="text-muted mb-0">Click on students above to select them for permanent deletion.</p>';

      // Load current members
      loadCurrentMembers(categoryId);
      loadAllStudentsForSystemDeletion();

      // Show modal
      const deleteModal = new bootstrap.Modal(document.getElementById('deleteMembersModal'));
      deleteModal.show();
    }

    // Load available students for adding
    function loadAvailableStudents(categoryId) {
      fetch(`/assignments/category/${categoryId}/available-students`)
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            populateAvailableStudents(data.students2025, data.students2026);
          } else {
            console.error('Error loading available students:', data.message);
          }
        })
        .catch(error => {
          console.error('Error:', error);
        });
    }

    // Populate available students in modal
    function populateAvailableStudents(students2025, students2026) {
      const container2025 = document.getElementById('availableStudents2025');
      const container2026 = document.getElementById('availableStudents2026');

      // Clear containers
      container2025.innerHTML = '';
      container2026.innerHTML = '';

      // Populate Batch 2025
      students2025.forEach(student => {
        const row = document.createElement('tr');
        row.className = 'student-row-add';
        row.style.cursor = 'pointer';
        row.dataset.studentId = student.id;
        row.dataset.studentName = student.name;
        row.dataset.studentBatch = student.batch;
        row.innerHTML = `<td class="py-2 px-3">${student.name} (${student.gender})</td>`;
        row.onclick = () => selectStudentToAdd(student);
        container2025.appendChild(row);
      });

      // Populate Batch 2026
      students2026.forEach(student => {
        const row = document.createElement('tr');
        row.className = 'student-row-add';
        row.style.cursor = 'pointer';
        row.dataset.studentId = student.id;
        row.dataset.studentName = student.name;
        row.dataset.studentBatch = student.batch;
        row.innerHTML = `<td class="py-2 px-3">${student.name} (${student.gender})</td>`;
        row.onclick = () => selectStudentToAdd(student);
        container2026.appendChild(row);
      });
    }

    // Select student to add
    function selectStudentToAdd(student) {
      // Check if already selected
      if (selectedStudentsToAdd.find(s => s.id === student.id)) {
        return;
      }

      selectedStudentsToAdd.push(student);
      updateSelectedStudentsToAddDisplay();

      // Highlight the row
      const rows = document.querySelectorAll(`[data-student-id="${student.id}"]`);
      rows.forEach(row => {
        row.classList.add('table-success');
        row.style.opacity = '0.6';
      });
    }

    // Update selected students to add display
    function updateSelectedStudentsToAddDisplay() {
      const container = document.getElementById('selectedStudentsToAdd');

      if (selectedStudentsToAdd.length === 0) {
        container.innerHTML = '<p class="text-muted mb-0">Click on students above to select them for adding to this category.</p>';
        return;
      }

      let html = '';
      selectedStudentsToAdd.forEach(student => {
        html += `
          <span class="badge bg-primary me-2 mb-2" style="font-size: 14px; padding: 8px 12px;">
            ${student.name} (Batch ${student.batch})
            <button type="button" class="btn-close btn-close-white ms-2" style="font-size: 10px;" onclick="removeStudentToAdd(${student.id})"></button>
          </span>
        `;
      });

      container.innerHTML = html;
    }

    // Remove student from add selection
    function removeStudentToAdd(studentId) {
      selectedStudentsToAdd = selectedStudentsToAdd.filter(s => s.id !== studentId);
      updateSelectedStudentsToAddDisplay();

      // Remove highlight from row
      const rows = document.querySelectorAll(`[data-student-id="${studentId}"]`);
      rows.forEach(row => {
        row.classList.remove('table-success');
        row.style.opacity = '1';
      });
    }

    // Add new student directly to category
    function addNewStudentToCategory(batch) {
      const nameInput = document.getElementById(`newStudent${batch}Name`);
      const genderSelect = document.getElementById(`newStudent${batch}Gender`);

      const name = nameInput.value.trim();
      const gender = genderSelect.value;

      if (!name) {
        alert('Please enter a student name');
        nameInput.focus();
        return;
      }

      // Create and add student to system, then assign to category
      fetch('/students/quick-add-to-category', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
          name: name,
          gender: gender,
          batch: batch,
          category_id: currentCategoryId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          showNotification('success', data.message);
          // Clear input
          nameInput.value = '';
          // Refresh available students
          loadAvailableStudents(currentCategoryId);
        } else {
          alert('Error adding student: ' + data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Error adding student');
      });
    }

    // Load current members for deleting
    function loadCurrentMembers(categoryId) {
      fetch(`/assignments/category/${categoryId}/current-members`)
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            populateCurrentMembers(data.members2025, data.members2026);
          } else {
            console.error('Error loading current members:', data.message);
          }
        })
        .catch(error => {
          console.error('Error:', error);
        });
    }

    // Populate current members in delete modal
    function populateCurrentMembers(members2025, members2026) {
      const container2025 = document.getElementById('currentMembers2025');
      const container2026 = document.getElementById('currentMembers2026');

      // Clear containers
      container2025.innerHTML = '';
      container2026.innerHTML = '';

      // Populate Batch 2025
      members2025.forEach(member => {
        const div = document.createElement('div');
        div.className = 'member-item-delete p-2 border rounded mb-2';
        div.style.cursor = 'pointer';
        div.dataset.memberId = member.id;
        div.dataset.memberName = member.student.name;
        div.innerHTML = `
          <div class="${member.is_coordinator ? 'coordinator-name' : ''}">${member.student.name}</div>
          ${member.comments ? `<small class="text-muted">(${member.comments})</small>` : ''}
        `;
        div.onclick = () => selectMemberToDelete(member);
        container2025.appendChild(div);
      });

      // Populate Batch 2026
      members2026.forEach(member => {
        const div = document.createElement('div');
        div.className = 'member-item-delete p-2 border rounded mb-2';
        div.style.cursor = 'pointer';
        div.dataset.memberId = member.id;
        div.dataset.memberName = member.student.name;
        div.innerHTML = `
          <div class="${member.is_coordinator ? 'coordinator-name' : ''}">${member.student.name}</div>
          ${member.comments ? `<small class="text-muted">(${member.comments})</small>` : ''}
        `;
        div.onclick = () => selectMemberToDelete(member);
        container2026.appendChild(div);
      });
    }

    // Select member to delete
    function selectMemberToDelete(member) {
      // Check if already selected
      if (selectedMembersToDelete.find(m => m.id === member.id)) {
        return;
      }

      selectedMembersToDelete.push(member);
      updateSelectedMembersToDeleteDisplay();

      // Highlight the member
      const items = document.querySelectorAll(`[data-member-id="${member.id}"]`);
      items.forEach(item => {
        item.classList.add('bg-danger', 'text-white');
      });
    }

    // Update selected members to delete display
    function updateSelectedMembersToDeleteDisplay() {
      const container = document.getElementById('selectedMembersToDelete');

      if (selectedMembersToDelete.length === 0) {
        container.innerHTML = '<p class="text-muted mb-0">Click on members above to select them for removal from this category.</p>';
        return;
      }

      let html = '';
      selectedMembersToDelete.forEach(member => {
        html += `
          <span class="badge bg-danger me-2 mb-2" style="font-size: 14px; padding: 8px 12px;">
            ${member.student.name}
            <button type="button" class="btn-close btn-close-white ms-2" style="font-size: 10px;" onclick="removeMemberToDelete(${member.id})"></button>
          </span>
        `;
      });

      container.innerHTML = html;
    }

    // Remove member from delete selection
    function removeMemberToDelete(memberId) {
      selectedMembersToDelete = selectedMembersToDelete.filter(m => m.id !== memberId);
      updateSelectedMembersToDeleteDisplay();

      // Remove highlight from member
      const items = document.querySelectorAll(`[data-member-id="${memberId}"]`);
      items.forEach(item => {
        item.classList.remove('bg-danger', 'text-white');
      });
    }

    // Load all students for system deletion
    function loadAllStudentsForSystemDeletion() {
      fetch('/students/all-for-deletion')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            populateStudentsForSystemDeletion(data.students2025, data.students2026);
          } else {
            console.error('Error loading students for deletion:', data.message);
          }
        })
        .catch(error => {
          console.error('Error:', error);
        });
    }

    // Populate students for system deletion
    function populateStudentsForSystemDeletion(students2025, students2026) {
      const container2025 = document.getElementById('systemDeleteMembers2025');
      const container2026 = document.getElementById('systemDeleteMembers2026');

      // Clear containers
      container2025.innerHTML = '';
      container2026.innerHTML = '';

      // Populate Batch 2025
      students2025.forEach(student => {
        const div = document.createElement('div');
        div.className = 'student-item-system-delete p-2 border rounded mb-2';
        div.style.cursor = 'pointer';
        div.dataset.studentId = student.id;
        div.dataset.studentName = student.name;
        div.innerHTML = `
          <div>${student.name} (${student.gender})</div>
        `;
        div.onclick = () => selectStudentForSystemDeletion(student);
        container2025.appendChild(div);
      });

      // Populate Batch 2026
      students2026.forEach(student => {
        const div = document.createElement('div');
        div.className = 'student-item-system-delete p-2 border rounded mb-2';
        div.style.cursor = 'pointer';
        div.dataset.studentId = student.id;
        div.dataset.studentName = student.name;
        div.innerHTML = `
          <div>${student.name} (${student.gender})</div>
        `;
        div.onclick = () => selectStudentForSystemDeletion(student);
        container2026.appendChild(div);
      });
    }

    // Select student for system deletion
    function selectStudentForSystemDeletion(student) {
      // Check if already selected
      if (selectedStudentsToDeleteFromSystem.find(s => s.id === student.id)) {
        return;
      }

      selectedStudentsToDeleteFromSystem.push(student);
      updateSelectedStudentsForSystemDeletionDisplay();

      // Highlight the student
      const items = document.querySelectorAll(`[data-student-id="${student.id}"]`);
      items.forEach(item => {
        if (item.classList.contains('student-item-system-delete')) {
          item.classList.add('bg-danger', 'text-white');
        }
      });
    }

    // Update selected students for system deletion display
    function updateSelectedStudentsForSystemDeletionDisplay() {
      const container = document.getElementById('selectedStudentsToDeleteFromSystem');

      if (selectedStudentsToDeleteFromSystem.length === 0) {
        container.innerHTML = '<p class="text-muted mb-0">Click on students above to select them for permanent deletion.</p>';
        return;
      }

      let html = '';
      selectedStudentsToDeleteFromSystem.forEach(student => {
        html += `
          <span class="badge bg-danger me-2 mb-2" style="font-size: 14px; padding: 8px 12px;">
            ${student.name}
            <button type="button" class="btn-close btn-close-white ms-2" style="font-size: 10px;" onclick="removeStudentFromSystemDeletion(${student.id})"></button>
          </span>
        `;
      });

      container.innerHTML = html;
    }

    // Remove student from system deletion selection
    function removeStudentFromSystemDeletion(studentId) {
      selectedStudentsToDeleteFromSystem = selectedStudentsToDeleteFromSystem.filter(s => s.id !== studentId);
      updateSelectedStudentsForSystemDeletionDisplay();

      // Remove highlight from student
      const items = document.querySelectorAll(`[data-student-id="${studentId}"]`);
      items.forEach(item => {
        if (item.classList.contains('student-item-system-delete')) {
          item.classList.remove('bg-danger', 'text-white');
        }
      });
    }

    // Open Edit Members Modal - Direct to edit form
    function openEditMembersModal(categoryId, categoryName) {
      // Close any existing modal first
      const existingModal = bootstrap.Modal.getInstance(document.getElementById('studentAssignModal' + categoryId));
      if (existingModal) {
        existingModal.hide();
      }

      // Set category name in modal title
      document.getElementById('editCategoryName').textContent = categoryName;

      // Show loading state
      document.getElementById('editMembers2025Container').innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';
      document.getElementById('editMembers2026Container').innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';

      // Show edit modal immediately
      const editModal = new bootstrap.Modal(document.getElementById('editMembersModal'));
      editModal.show();

      // Fetch members data and populate edit form directly
      fetch(`/assignments/category/${categoryId}/members`)
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            populateEditMembers(data.members2025, data.members2026);
          } else {
            showNotification('Error loading members data', 'error');
          }
        })
        .catch(error => {
          console.error('Error:', error);
          showNotification('Error loading members data', 'error');
        });
    }

    // Populate edit members containers
    function populateEditMembers(members2025, members2026) {
      const container2025 = document.getElementById('editMembers2025Container');
      const container2026 = document.getElementById('editMembers2026Container');

      // Clear containers
      container2025.innerHTML = '';
      container2026.innerHTML = '';

      // Populate Batch 2025
      members2025.forEach(member => {
        const memberHtml = createEditMemberRow(member);
        container2025.appendChild(memberHtml);
      });

      // Populate Batch 2026
      members2026.forEach(member => {
        const memberHtml = createEditMemberRow(member);
        container2026.appendChild(memberHtml);
      });
    }

    // Create edit member row
    function createEditMemberRow(member) {
      const div = document.createElement('div');
      div.className = `member-row ${member.is_coordinator ? 'coordinator-highlight-edit' : ''}`;
      div.id = `member-row-${member.id}`;

      div.innerHTML = `
        <div class="member-info">
          <div class="member-name ${member.is_coordinator ? 'coordinator-name' : ''}">
            ${member.student.name}
          </div>
          ${member.comments ? `<div class="member-comment" id="comment-display-${member.id}">(${member.comments})</div>` : ''}
          <div class="comment-edit-area" id="comment-edit-${member.id}" style="display: none;">
            <input type="text" class="comment-input" id="comment-input-${member.id}"
                   value="${member.comments || ''}" placeholder="Add comment (e.g., Not available - headache)">
          </div>
        </div>
        <div class="member-actions">
          <button class="btn-edit-small" onclick="editMember(${member.id})" id="edit-btn-${member.id}">Edit</button>
          <button class="btn-save-small" onclick="saveMember(${member.id})" id="save-btn-${member.id}" style="display: none;">Save</button>
        </div>
      `;

      return div;
    }

    // Edit member function
    function editMember(memberId) {
      // Hide comment display and edit button
      const commentDisplay = document.getElementById(`comment-display-${memberId}`);
      const editBtn = document.getElementById(`edit-btn-${memberId}`);
      const saveBtn = document.getElementById(`save-btn-${memberId}`);
      const commentEdit = document.getElementById(`comment-edit-${memberId}`);

      if (commentDisplay) commentDisplay.style.display = 'none';
      editBtn.style.display = 'none';
      saveBtn.style.display = 'inline-block';
      commentEdit.style.display = 'block';

      // Focus on input
      const input = document.getElementById(`comment-input-${memberId}`);
      input.focus();
    }

    // Edit student name
    function editStudentName(studentId) {
      const nameDisplay = document.getElementById(`name-display-${studentId}`);
      const nameEdit = document.getElementById(`name-edit-${studentId}`);
      const editBtn = document.getElementById(`edit-name-btn-${studentId}`);
      const saveBtn = document.getElementById(`save-name-btn-${studentId}`);
      const cancelBtn = document.getElementById(`cancel-name-btn-${studentId}`);

      nameDisplay.classList.add('d-none');
      nameEdit.classList.remove('d-none');
      editBtn.classList.add('d-none');
      saveBtn.classList.remove('d-none');
      cancelBtn.classList.remove('d-none');

      nameEdit.focus();
      nameEdit.select();
    }

    // Save student name
    function saveStudentName(studentId) {
      const nameEdit = document.getElementById(`name-edit-${studentId}`);
      const newName = nameEdit.value.trim();

      if (!newName) {
        alert('Name cannot be empty');
        return;
      }

      fetch(`/students/${studentId}/update-name`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ name: newName })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const nameDisplay = document.getElementById(`name-display-${studentId}`);
          nameDisplay.textContent = newName;
          cancelEditName(studentId);

          // Show success message
          showNotification('success', data.message);
        } else {
          alert('Error updating name: ' + data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Error updating student name');
      });
    }

    // Cancel edit name
    function cancelEditName(studentId) {
      const nameDisplay = document.getElementById(`name-display-${studentId}`);
      const nameEdit = document.getElementById(`name-edit-${studentId}`);
      const editBtn = document.getElementById(`edit-name-btn-${studentId}`);
      const saveBtn = document.getElementById(`save-name-btn-${studentId}`);
      const cancelBtn = document.getElementById(`cancel-name-btn-${studentId}`);

      nameDisplay.classList.remove('d-none');
      nameEdit.classList.add('d-none');
      editBtn.classList.remove('d-none');
      saveBtn.classList.add('d-none');
      cancelBtn.classList.add('d-none');

      // Reset input value to original
      nameEdit.value = nameDisplay.textContent;
    }

    // Delete student
    function deleteStudent(studentId, studentName) {
      if (!confirm(`Are you sure you want to delete "${studentName}"? This action cannot be undone and will remove the student from all assignments.`)) {
        return;
      }

      fetch(`/students/${studentId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          showNotification('success', data.message);
          // Refresh the page to update the view
          setTimeout(() => {
            location.reload();
          }, 1500);
        } else {
          alert('Error deleting student: ' + data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Error deleting student');
      });
    }

    // Add new student
    function addNewStudent(batch) {
      const nameInput = document.getElementById(`newStudent${batch}Name`);
      const genderSelect = document.getElementById(`newStudent${batch}Gender`);

      const name = nameInput.value.trim();
      const gender = genderSelect.value;

      if (!name) {
        alert('Please enter a student name');
        nameInput.focus();
        return;
      }

      fetch('/students/quick-add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
          name: name,
          gender: gender,
          batch: batch
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          showNotification('success', data.message);
          nameInput.value = '';
          // Refresh the page to update the view
          setTimeout(() => {
            location.reload();
          }, 1500);
        } else {
          alert('Error adding student: ' + data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Error adding student');
      });
    }

    // Show notification
    function showNotification(type, message) {
      const notification = document.createElement('div');
      notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
      notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
      notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      `;

      document.body.appendChild(notification);

      // Auto remove after 3 seconds
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove();
        }
      }, 3000);
    }

    // Save member function
    function saveMember(memberId) {
      const input = document.getElementById(`comment-input-${memberId}`);
      const comments = input.value.trim();

      // Show loading state
      const saveBtn = document.getElementById(`save-btn-${memberId}`);
      const originalText = saveBtn.textContent;
      saveBtn.textContent = 'Saving...';
      saveBtn.disabled = true;

      // Get CSRF token
      const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

      // Create form data
      const formData = new FormData();
      formData.append('member_id', memberId);
      formData.append('comments', comments);
      formData.append('_token', csrfToken);

      // Send AJAX request
      fetch('/assignments/update-member-comment', {
        method: 'POST',
        headers: {
          'X-CSRF-TOKEN': csrfToken,
          'Accept': 'application/json'
        },
        body: formData
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        if (data.success) {
          // Update display
          const commentDisplay = document.getElementById(`comment-display-${memberId}`);
          const editBtn = document.getElementById(`edit-btn-${memberId}`);
          const commentEdit = document.getElementById(`comment-edit-${memberId}`);

          if (comments) {
            if (commentDisplay) {
              commentDisplay.textContent = `(${comments})`;
              commentDisplay.style.display = 'block';
            } else {
              // Create comment display if it doesn't exist
              const memberInfo = document.querySelector(`#member-row-${memberId} .member-info`);
              const newCommentDisplay = document.createElement('div');
              newCommentDisplay.className = 'member-comment';
              newCommentDisplay.id = `comment-display-${memberId}`;
              newCommentDisplay.textContent = `(${comments})`;
              memberInfo.appendChild(newCommentDisplay);
            }
          } else {
            if (commentDisplay) commentDisplay.style.display = 'none';
          }

          // Reset buttons
          editBtn.style.display = 'inline-block';
          saveBtn.style.display = 'none';
          commentEdit.style.display = 'none';

          // Show success message
          showNotification('Comment saved successfully!', 'success');

          // Refresh the page after 1 second to show updated comments in View History
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } else {
          showNotification(data.message || 'Error saving comment. Please try again.', 'error');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showNotification('Error saving comment. Please try again.', 'error');
      })
      .finally(() => {
        // Reset button state
        saveBtn.textContent = originalText;
        saveBtn.disabled = false;
      });
    }

    // Simple notification function
    function showNotification(message, type) {
      const notification = document.createElement('div');
      notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
      notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
      notification.textContent = message;

      document.body.appendChild(notification);

      setTimeout(() => {
        notification.remove();
      }, 3000);
    }

    // Optional: Add some visual feedback when hovering over edit button
    document.addEventListener('DOMContentLoaded', function() {
      const editButtons = document.querySelectorAll('.btn-edit-blue');
      editButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-2px)';
        });
        button.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0)';
        });
      });
    });

    // Task Checklist Modal Functions
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('status-btn')) {
            const taskId = e.target.dataset.task;
            const week = e.target.dataset.week;
            const day = e.target.dataset.day;
            const status = e.target.dataset.status;

            // Find other button in same cell and deactivate it
            const otherButtons = e.target.parentElement.querySelectorAll('.status-btn');
            otherButtons.forEach(btn => {
                if (btn !== e.target) {
                    btn.classList.remove('active');
                }
            });

            // Toggle current button
            if (e.target.classList.contains('active')) {
                e.target.classList.remove('active');
            } else {
                e.target.classList.add('active');
            }
        }
    });

    // Update week dates function
    function updateWeekDates() {
        const week1Date = document.getElementById('week1_date').value;
        const week2Date = document.getElementById('week2_date').value;

        // You can add AJAX call here to save dates if needed
        console.log('Week 1 Date:', week1Date);
        console.log('Week 2 Date:', week2Date);
    }

    // Mark task function for check/wrong buttons
    function markTask(cellId, action) {
        // Find the button that was clicked
        const cell = document.querySelector(`[onclick*="${cellId}"]`).closest('td');
        const buttons = cell.querySelectorAll('button');

        // Reset all buttons in this cell
        buttons.forEach(btn => {
            btn.style.opacity = '0.5';
            btn.style.transform = 'scale(1)';
        });

        // Highlight the selected button
        const selectedButton = Array.from(buttons).find(btn =>
            (action === 'check' && btn.textContent === '✓') ||
            (action === 'wrong' && btn.textContent === '✗')
        );

        if (selectedButton) {
            selectedButton.style.opacity = '1';
            selectedButton.style.fontWeight = 'bold';
            selectedButton.style.transform = 'scale(1.1)';
        }

        console.log(`Task ${cellId} marked as ${action}`);
    }

    // Task Page Navigation
    let currentTaskPage = 1;
    const totalTaskPages = 10; // You can adjust this based on how many pages you want

    function navigateTaskPage(direction) {
        if (direction === 'next') {
            currentTaskPage = currentTaskPage < totalTaskPages ? currentTaskPage + 1 : 1;
        } else if (direction === 'prev') {
            currentTaskPage = currentTaskPage > 1 ? currentTaskPage - 1 : totalTaskPages;
        }

        console.log('Current Task Page:', currentTaskPage);

        // Update page number display
        document.getElementById('currentPageNumber').textContent = currentTaskPage;

        // Here you can load different content based on currentTaskPage
        loadTaskPageContent(currentTaskPage);
    }

    function loadTaskPageContent(pageNumber) {
        console.log('Loading task page:', pageNumber);

        const taskPageContent = document.getElementById('taskPageContent');

        if (pageNumber === 1) {
            // Page 1: Kitchen & General Cleaning Tasks
            taskPageContent.innerHTML = getPage1Content();
        } else if (pageNumber === 2) {
            // Page 2: Dishwashing & General Cleaning Tasks
            taskPageContent.innerHTML = getPage2Content();
        } else if (pageNumber === 3) {
            // Page 3: Dining & Ground Floor Cleaning
            taskPageContent.innerHTML = getPage3Content();
        } else if (pageNumber === 4) {
            // Page 4: Laundry & Maintenance
            taskPageContent.innerHTML = getPage4Content();
        } else if (pageNumber === 5) {
            // Page 5: Security & Safety
            taskPageContent.innerHTML = getPage5Content();
        } else if (pageNumber === 6) {
            // Page 6: Office & Administrative
            taskPageContent.innerHTML = getPage6Content();
        } else if (pageNumber === 7) {
            // Page 7: Garden & Outdoor
            taskPageContent.innerHTML = getPage7Content();
        } else if (pageNumber === 8) {
            // Page 8: Storage & Inventory
            taskPageContent.innerHTML = getPage8Content();
        } else if (pageNumber === 9) {
            // Page 9: Recreation & Events
            taskPageContent.innerHTML = getPage9Content();
        } else if (pageNumber === 10) {
            // Page 10: Special Tasks & Projects
            taskPageContent.innerHTML = getPage10Content();
        } else {
            // Default to Page 1
            taskPageContent.innerHTML = getPage1Content();
        }

        // Update modal title to show current page
        document.getElementById('taskChecklistModalLabel').textContent = `Task Checklist - Page ${pageNumber}`;
    }

    function getPage1Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 1px solid #dee2e6; width: 100%; min-width: 1600px;">
              <thead>
                <tr>
                  <th rowspan="3" class="text-center" style="background-color: transparent; border: 1px solid #dee2e6; font-size: 14px; font-weight: 600; padding: 12px; vertical-align: middle; width: 120px;">TASK AREAS</th>
                  <th rowspan="3" class="text-center" style="background-color: transparent; border: 1px solid #dee2e6; font-size: 14px; font-weight: 600; padding: 8px; vertical-align: middle; width: 400px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: transparent; border: 1px solid #dee2e6; padding: 8px; font-size: 12px; font-weight: 600;">
                    DATE: <input type="date" id="week1_date" value="<?php echo e(now()->startOfWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 140px; font-size: 12px; padding: 4px; margin-left: 8px; border: 1px solid #dee2e6;" onchange="updateWeekDates()">
                  </th>
                  <th rowspan="3" class="text-center" style="width: 100px; background-color: transparent; border: 1px solid #dee2e6; font-size: 12px; font-weight: 600; color: #666;">REMARKS</th>
                  <th colspan="7" class="text-center" style="background-color: transparent; border: 1px solid #dee2e6; padding: 8px; font-size: 12px; font-weight: 600;">
                    DATE: <input type="date" id="week2_date" value="<?php echo e(now()->startOfWeek()->addWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 140px; font-size: 12px; padding: 4px; margin-left: 8px; border: 1px solid #dee2e6;" onchange="updateWeekDates()">
                  </th>
                  <th rowspan="3" class="text-center" style="width: 100px; background-color: transparent; border: 1px solid #dee2e6; font-size: 12px; font-weight: 600; color: #666;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 60px; border: 1px solid #dee2e6; font-size: 10px; padding: 4px; background-color: transparent; font-weight: 600;">MON</th>
                  <th class="text-center" style="width: 60px; border: 1px solid #dee2e6; font-size: 10px; padding: 4px; background-color: transparent; font-weight: 600;">TUE</th>
                  <th class="text-center" style="width: 60px; border: 1px solid #dee2e6; font-size: 10px; padding: 4px; background-color: transparent; font-weight: 600;">WED</th>
                  <th class="text-center" style="width: 60px; border: 1px solid #dee2e6; font-size: 10px; padding: 4px; background-color: transparent; font-weight: 600;">THU</th>
                  <th class="text-center" style="width: 60px; border: 1px solid #dee2e6; font-size: 10px; padding: 4px; background-color: transparent; font-weight: 600;">FRI</th>
                  <th class="text-center" style="width: 60px; border: 1px solid #dee2e6; font-size: 10px; padding: 4px; background-color: transparent; font-weight: 600;">SAT</th>
                  <th class="text-center" style="width: 60px; border: 1px solid #dee2e6; font-size: 10px; padding: 4px; background-color: transparent; font-weight: 600;">SUN</th>
                  <th class="text-center" style="width: 60px; border: 1px solid #dee2e6; font-size: 10px; padding: 4px; background-color: transparent; font-weight: 600;">MON</th>
                  <th class="text-center" style="width: 60px; border: 1px solid #dee2e6; font-size: 10px; padding: 4px; background-color: transparent; font-weight: 600;">TUE</th>
                  <th class="text-center" style="width: 60px; border: 1px solid #dee2e6; font-size: 10px; padding: 4px; background-color: transparent; font-weight: 600;">WED</th>
                  <th class="text-center" style="width: 60px; border: 1px solid #dee2e6; font-size: 10px; padding: 4px; background-color: transparent; font-weight: 600;">THU</th>
                  <th class="text-center" style="width: 60px; border: 1px solid #dee2e6; font-size: 10px; padding: 4px; background-color: transparent; font-weight: 600;">FRI</th>
                  <th class="text-center" style="width: 60px; border: 1px solid #dee2e6; font-size: 10px; padding: 4px; background-color: transparent; font-weight: 600;">SAT</th>
                  <th class="text-center" style="width: 60px; border: 1px solid #dee2e6; font-size: 10px; padding: 4px; background-color: transparent; font-weight: 600;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getKitchenRows()}
                ${getGeneralCleaningRows()}
              </tbody>
            </table>
        `;
    }

    function getKitchenRows() {
        const kitchenTasks = [
            'Assigned members wake up on time and completed their tasks as scheduled.',
            'The students assigned to cook the rice completed the task properly.',
            'The students assigned to cook the viand completed the task properly.',
            'The students assigned to assist the cook carried out their duties diligently.',
            'Ingredients were prepared ahead of time.',
            'The kitchen was properly cleaned after cooking.',
            'The food was transferred from the kitchen to the center.',
            'Proper inventory of stocks was maintained and deliveries were handled appropriately.',
            'Water and food supplies were regularly monitored and stored in the proper place.',
            'Receipts, kitchen phones, and keys were safely stored.',
            'Kitchen utensils were properly stored.',
            'The stove was turned off after cooking.',
            'Properly disposed of the garbage.',
            'Properly washed the burner.',
            'Wiped and arranged the chiller.',
            'Cleaned the canal after cooking.',
            'Arranged the freezer.'
        ];

        let rows = '';
        kitchenTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${kitchenTasks.length}" class="text-center" style="background: linear-gradient(135deg, #4caf50, #66bb6a); color: white; border: 1px solid #dee2e6; font-size: 12px; font-weight: 600; padding: 15px; vertical-align: middle; writing-mode: vertical-rl; text-orientation: mixed;">KITCHEN<br><small style="font-size: 8px; font-weight: normal;">2-3 weeks before the assigned day</small></td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td style="border: 1px solid #dee2e6; padding: 8px; font-size: 11px; background-color: #fafafa; text-align: left;">
                        ${task}
                    </td>
                    ${generateCheckboxCells('kitchen' + (index + 1))}
                    <td style="border: 1px solid #dee2e6; padding: 2px; background-color: #fff8e1;">
                        <input type="text" class="form-control" placeholder="Remarks..." style="font-size: 9px; padding: 2px; border: 1px solid #dee2e6; color: #666; font-weight: normal;">
                    </td>
                    ${generateCheckboxCells('kitchen' + (index + 1), 'week2')}
                    <td style="border: 1px solid #dee2e6; padding: 2px; background-color: #fff8e1;">
                        <input type="text" class="form-control" placeholder="Remarks..." style="font-size: 9px; padding: 2px; border: 1px solid #dee2e6; color: #666; font-weight: normal;">
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGeneralCleaningRows() {
        const cleaningTasks = [
            'Cleaned the drainage canals.',
            'Brushed and rinsed the floor of the dishwashing area.',
            'Brushed the sink.',
            'Washed the barrel container.',
            'Cleaned and arranged the storage cabinet.',
            'Wiped the cabinets. (No dusts/stains inside and outside the cabinet)'
        ];

        let rows = '';
        cleaningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${cleaningTasks.length}" class="text-center category-cell" style="background: linear-gradient(135deg, #2196f3, #42a5f5); color: white; border: 1px solid #dee2e6; font-size: 14px; font-weight: 600; padding: 12px; vertical-align: middle;">GENERAL<br>CLEANING</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('cleaning' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="cleaning${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('cleaning' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="cleaning${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function generateCheckboxCells(taskId, week = 'week1') {
        const days = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
        let cells = '';

        days.forEach(day => {
            const cellId = `${taskId}_${day}_${week}`;
            cells += `
                <td class="text-center" style="border: 1px solid #dee2e6; padding: 4px; background-color: #f8f9fa;">
                    <div style="display: flex; flex-direction: column; gap: 2px; align-items: center;">
                        <button type="button" class="btn btn-success btn-sm" style="font-size: 10px; padding: 2px 6px; margin: 1px; background-color: #4caf50; border: 1px solid #4caf50; color: white; font-weight: 600; width: 35px; height: 20px; border-radius: 4px;" onclick="markTask('${cellId}', 'check')" title="Check">✓</button>
                        <button type="button" class="btn btn-danger btn-sm" style="font-size: 10px; padding: 2px 6px; margin: 1px; background-color: #f44336; border: 1px solid #f44336; color: white; font-weight: 600; width: 35px; height: 20px; border-radius: 4px;" onclick="markTask('${cellId}', 'wrong')" title="Wrong">✗</button>
                    </div>
                </td>
            `;
        });

        return cells;
    }

    function getPage2Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 2px solid #000; width: 100%; min-width: 1600px;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #ffc107; color: white; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 80px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 300px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 1 - DATE: <input type="date" id="week1_date_p2" value="<?php echo e(now()->startOfWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 2 - DATE: <input type="date" id="week2_date_p2" value="<?php echo e(now()->startOfWeek()->addWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getDishwashingRows()}
                ${getGeneralCleaningRows()}
              </tbody>
            </table>
        `;
    }

    function getDishwashingRows() {
        const dishwashingTasks = [
            'Wash the dishes thoroughly.',
            'Disposed of the leftovers in the proper place.',
            'Cleaned the sink after washing the dishes.',
            'Ensured no plates, glasses, utensils, or other items were left in the sink.',
            'Neatly arranged the plates, glasses, utensils, pots, and pans in their designated places.',
            'Properly stored the basin and pail in their designated areas.',
            'Avoid wasting soap during washing.',
            'Cleaned the dishwashing area.',
            'Ensured staff plates, utensils, and other items were properly cleaned and stored in their designated areas.'
        ];

        let rows = '';
        dishwashingTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${dishwashingTasks.length}" class="text-center category-cell" style="background-color: #ffc107; color: white; border: 1px solid #000; font-size: 14px; font-weight: bold; padding: 12px; vertical-align: middle;">DISHWASHING<br><br>2-3<br>unchecked-<br>for<br>improvement<br>4 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('dishwashing' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="dishwashing${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('dishwashing' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="dishwashing${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getPage3Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 2px solid #000; width: 100%; min-width: 1600px;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #17a2b8; color: white; border: 1px solid #000; font-size: 16px; font-weight: bold; padding: 12px; vertical-align: middle; width: 100px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 300px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 1 - DATE: <input type="date" id="week1_date_p3" value="<?php echo e(now()->startOfWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 2 - DATE: <input type="date" id="week2_date_p3" value="<?php echo e(now()->startOfWeek()->addWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getDiningRows()}
                ${getGroundFloorRows()}
              </tbody>
            </table>
        `;
    }

    function getDiningRows() {
        const diningTasks = [
            'Set up the dining area ahead of time.',
            'Distributed the food equally.',
            'Properly wiped the tables after mealtime.',
            'Rang the bell or announce to batchmates that it\'s mealtime.',
            'Swept the dining area.',
            'Arranged and cleaned the dining area after mealtime (chairs, tables, and dishes).',
            'Packed the lunch of batchmates on time.',
            'Gathered all the dishes for washing.'
        ];

        let rows = '';
        diningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${diningTasks.length}" class="text-center category-cell" style="background-color: #17a2b8; color: white; border: 1px solid #000; font-size: 14px; font-weight: bold; padding: 12px; vertical-align: middle;">DINING<br><br>2<br>unchecked-<br>for<br>improvement<br>3 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('dining' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="dining${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('dining' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="dining${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGroundFloorRows() {
        const groundFloorTasks = [
            'Brushed the tables.',
            'Brush and rinse the floor in the dining area.',
            'Arrange the dining area once the tables and floor are dry.'
        ];

        let rows = '';
        groundFloorTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${groundFloorTasks.length}" class="text-center category-cell" style="background-color: #17a2b8; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">General<br>Cleaning</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('generalcleaning' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('generalcleaning' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    // Page 4: Room 203 - Offices & Conference Rooms
    function getPage4Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 2px solid #000; width: 100%; min-width: 1600px;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 16px; font-weight: bold; padding: 12px; vertical-align: middle; width: 100px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 300px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 1 - DATE: <input type="date" id="week1_date_p4" value="<?php echo e(now()->startOfWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 2 - DATE: <input type="date" id="week2_date_p4" value="<?php echo e(now()->startOfWeek()->addWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getOfficesConferenceRoomsRows()}
                ${getGeneralCleaningRows4()}
              </tbody>
            </table>
        `;
    }

    function getOfficesConferenceRoomsRows() {
        const officesTasks = [
            'ROOM 203',
            'Properly cleaned and brushed the toilet, sink, and shower room, including the tiles.',
            'Swept the floor.',
            'Mopped the floor.',
            'Wiped the tables and chairs (dust-free).',
            'Wiped the mirror with a cloth or paper.',
            'Wiped the cabinets (dust-free).',
            'Cleaned and organized the plates, glasses, and spoons.',
            'Ensured the pail in the toilet is full of water.',
            'Cleaned the window.',
            'Cleaned the toilet bowl and tiles with a cleaner.',
            'Got rid of the cobwebs.',
            'Removed stains on the wall.'
        ];

        let rows = '';
        officesTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const isRoomTitle = task === 'ROOM 203';
            const categoryCell = isFirst ? `<td rowspan="${officesTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 14px; font-weight: bold; padding: 12px; vertical-align: middle;">OFFICES &<br>CONFERENCE<br>ROOMS<br><br>2-3<br>unchecked-<br>for<br>improvement<br>4 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            const taskCellClass = isRoomTitle ? 'task-cell room-title' : 'task-cell';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="${taskCellClass}">${task}</td>
                    ${generateCheckboxCells('offices' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="offices${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('offices' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="offices${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGeneralCleaningRows4() {
        const generalCleaningTasks = [
            'Cleaned the toilet bowl and tiles with a cleaner.',
            'Got rid of the cobwebs.',
            'Removed stains on the wall.'
        ];

        let rows = '';
        generalCleaningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${generalCleaningTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle; writing-mode: vertical-rl; text-orientation: mixed;">General<br>Cleaning</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('generalcleaning4_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning4_${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('generalcleaning4_' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning4_${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    // Page 5: Room 301 - Offices & Conference Rooms
    function getPage5Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 2px solid #000; width: 100%; min-width: 1600px;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #fd7e14; color: white; border: 1px solid #000; font-size: 16px; font-weight: bold; padding: 12px; vertical-align: middle; width: 100px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 300px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 1 - DATE: <input type="date" id="week1_date_p5" value="<?php echo e(now()->startOfWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 2 - DATE: <input type="date" id="week2_date_p5" value="<?php echo e(now()->startOfWeek()->addWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getOfficesConferenceRoomsRows5()}
                ${getGeneralCleaningRows5()}
              </tbody>
            </table>
        `;
    }

    function getOfficesConferenceRoomsRows5() {
        const officesTasks = [
            'ROOM 301',
            'Properly cleaned and brushed the toilet, sink, and shower room, including the tiles.',
            'Swept the floor.',
            'Mopped the floor.',
            'Wiped the tables and chairs (dust-free).',
            'Wiped the mirror with a cloth or paper.',
            'Wiped the cabinets (dust-free).',
            'Cleaned and organized the plates, glasses, and spoons.',
            'Ensured the pail in the toilet is full of water.',
            'Cleaned the window.',
            'Washed the curtain on Saturday.',
            'Hung the curtain on Sunday.'
        ];

        let rows = '';
        officesTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const isRoomTitle = task === 'ROOM 301';
            const categoryCell = isFirst ? `<td rowspan="${officesTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">OFFICES &<br>CONFERENCE<br>ROOMS<br><br>2-3<br>unchecked-<br>for<br>improvement<br>4 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            const taskCellStyle = isRoomTitle ? 'background-color: #ffff00; font-weight: bold;' : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell" style="${taskCellStyle}">${task}</td>
                    ${generateCheckboxCells('offices5_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="offices5_${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('offices5_' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="offices5_${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGeneralCleaningRows5() {
        const generalCleaningTasks = [
            'Cleaned the toilet bowl and tiles with a cleaner.',
            'Got rid of the cobwebs.',
            'Removed stains on the wall.'
        ];

        let rows = '';
        generalCleaningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${generalCleaningTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">General<br>Cleaning</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('generalcleaning5_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning5_${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('generalcleaning5_' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning5_${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    // Page 6: Room 401 - Offices & Conference Rooms
    function getPage6Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 2px solid #000; width: 100%; min-width: 1600px;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #20c997; color: white; border: 1px solid #000; font-size: 16px; font-weight: bold; padding: 12px; vertical-align: middle; width: 100px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 300px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 1 - DATE: <input type="date" id="week1_date_p6" value="<?php echo e(now()->startOfWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 2 - DATE: <input type="date" id="week2_date_p6" value="<?php echo e(now()->startOfWeek()->addWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getOfficesConferenceRoomsRows6()}
                ${getGeneralCleaningRows6()}
              </tbody>
            </table>
        `;
    }

    function getOfficesConferenceRoomsRows6() {
        const officesTasks = [
            'ROOM 401',
            'Properly cleaned and brushed the toilet, sink, and shower room, including the tiles.',
            'Swept the floor.',
            'Mopped the floor.',
            'Wiped the tables and chairs (dust-free).',
            'Wiped the mirror with a cloth or paper.',
            'Wiped the cabinets (dust-free).',
            'Cleaned and organized the plates, glasses, and spoons.',
            'Ensured the pail in the toilet is full of water.',
            'Cleaned the window.'
        ];

        let rows = '';
        officesTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const isRoomTitle = task === 'ROOM 401';
            const categoryCell = isFirst ? `<td rowspan="${officesTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">OFFICES &<br>CONFERENCE<br>ROOMS<br><br>2-3<br>unchecked-<br>for<br>improvement<br>4 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            const taskCellStyle = isRoomTitle ? 'background-color: #ffff00; font-weight: bold;' : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell" style="${taskCellStyle}">${task}</td>
                    ${generateCheckboxCells('offices6_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="offices6_${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('offices6_' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="offices6_${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGeneralCleaningRows6() {
        const generalCleaningTasks = [
            'Cleaned the toilet bowl and tiles with a cleaner.',
            'Got rid of the cobwebs.',
            'Removed stains on the wall.'
        ];

        let rows = '';
        generalCleaningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${generalCleaningTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">General<br>Cleaning</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('generalcleaning6_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning6_${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('generalcleaning6_' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning6_${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    // Page 7: Room 303 - Offices & Conference Rooms
    function getPage7Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 2px solid #000; width: 100%; min-width: 1600px;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #198754; color: white; border: 1px solid #000; font-size: 16px; font-weight: bold; padding: 12px; vertical-align: middle; width: 100px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 300px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 1 - DATE: <input type="date" id="week1_date_p7" value="<?php echo e(now()->startOfWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 2 - DATE: <input type="date" id="week2_date_p7" value="<?php echo e(now()->startOfWeek()->addWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getOfficesConferenceRoomsRows7()}
                ${getGeneralCleaningRows7()}
              </tbody>
            </table>
        `;
    }

    function getOfficesConferenceRoomsRows7() {
        const officesTasks = [
            'ROOM 303',
            'Properly cleaned and brushed the toilet, sink, and shower room, including the tiles.',
            'Swept the floor.',
            'Mopped the floor.',
            'Wiped the tables and chairs (dust-free).',
            'Wiped the mirror with a cloth or paper.',
            'Wiped the cabinets (dust-free).',
            'Cleaned and organized the plates, glasses, and spoons.',
            'Ensured the pail in the toilet is full of water.',
            'Cleaned the window.'
        ];

        let rows = '';
        officesTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const isRoomTitle = task === 'ROOM 303';
            const categoryCell = isFirst ? `<td rowspan="${officesTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">OFFICES &<br>CONFERENCE<br>ROOMS<br><br>2-3<br>unchecked-<br>for<br>improvement<br>4 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            const taskCellStyle = isRoomTitle ? 'background-color: #ffff00; font-weight: bold;' : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell" style="${taskCellStyle}">${task}</td>
                    ${generateCheckboxCells('offices7_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="offices7_${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('offices7_' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="offices7_${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGeneralCleaningRows7() {
        const generalCleaningTasks = [
            'Cleaned the toilet bowl and tiles with a cleaner.',
            'Got rid of the cobwebs.',
            'Removed stains on the wall.'
        ];

        let rows = '';
        generalCleaningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${generalCleaningTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">General<br>Cleaning</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('generalcleaning7_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning7_${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('generalcleaning7_' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning7_${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    // Page 8: Room 503 - Offices & Conference Rooms
    function getPage8Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 2px solid #000; width: 100%; min-width: 1600px;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #6610f2; color: white; border: 1px solid #000; font-size: 16px; font-weight: bold; padding: 12px; vertical-align: middle; width: 100px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 300px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 1 - DATE: <input type="date" id="week1_date_p8" value="<?php echo e(now()->startOfWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 2 - DATE: <input type="date" id="week2_date_p8" value="<?php echo e(now()->startOfWeek()->addWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getOfficesConferenceRoomsRows8()}
                ${getGeneralCleaningRows8()}
              </tbody>
            </table>
        `;
    }

    function getOfficesConferenceRoomsRows8() {
        const officesTasks = [
            'ROOM 503',
            'Properly cleaned and brushed the toilet, sink, and shower room, including the tiles.',
            'Swept the floor.',
            'Mopped the floor.',
            'Wiped the tables and chairs (dust-free).',
            'Wiped the mirror with a cloth or paper.',
            'Wiped the cabinets (dust-free).',
            'Cleaned and organized the electronic devices.',
            'Ensured the pail in the toilet is full of water.',
            'Cleaned the window.'
        ];

        let rows = '';
        officesTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const isRoomTitle = task === 'ROOM 503';
            const categoryCell = isFirst ? `<td rowspan="${officesTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">OFFICES &<br>CONFERENCE<br>ROOMS<br><br>2-3<br>unchecked-<br>for<br>improvement<br>4 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            const taskCellStyle = isRoomTitle ? 'background-color: #ffff00; font-weight: bold;' : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell" style="${taskCellStyle}">${task}</td>
                    ${generateCheckboxCells('offices8_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="offices8_${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('offices8_' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="offices8_${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGeneralCleaningRows8() {
        const generalCleaningTasks = [
            'Cleaned the toilet bowl and tiles with a cleaner.',
            'Got rid of the cobwebs.',
            'Removed stains on the wall.'
        ];

        let rows = '';
        generalCleaningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${generalCleaningTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">General<br>Cleaning</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('generalcleaning8_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning8_${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('generalcleaning8_' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning8_${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    // Page 9: Garbage Collection
    function getPage9Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 2px solid #000; width: 100%; min-width: 1600px;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #e83e8c; color: white; border: 1px solid #000; font-size: 16px; font-weight: bold; padding: 12px; vertical-align: middle; width: 100px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 300px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 1 - DATE: <input type="date" id="week1_date_p9" value="<?php echo e(now()->startOfWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 2 - DATE: <input type="date" id="week2_date_p9" value="<?php echo e(now()->startOfWeek()->addWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getGarbageCollectionRows()}
                ${getGeneralCleaningRows9()}
              </tbody>
            </table>
        `;
    }

    function getGarbageCollectionRows() {
        const garbageTasks = [
            'Collected all the trash from conference rooms, offices (inside the office trash bins), rooms, rooftop, ground floor, and other areas with trash.',
            'Ensured that all rooms and offices had their own trash bins.',
            'Segregated the garbage.',
            'Washed the rugs and sofa covers.',
            'Placed the rugs in their designated areas.',
            'Threw away items placed in the fire exit.',
            'Washed the trash bins.',
            'Arranged the items on the rooftop.'
        ];

        let rows = '';
        garbageTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${garbageTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 14px; font-weight: bold; padding: 12px; vertical-align: middle;">GARBAGE<br>COLLECTORS,<br>RUGS &<br>ROOFTOP<br><br>2<br>unchecked-<br>for<br>improvement<br>3 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('garbage' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="garbage${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('garbage' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="garbage${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGeneralCleaningRows9() {
        const generalCleaningTasks = [
            'Cleaned and rinsed the floor on the rooftop.',
            'Wiped the rooftop window.',
            'Returned the trash bins to their designated areas by Sunday afternoon.'
        ];

        let rows = '';
        generalCleaningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${generalCleaningTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">General<br>Cleaning</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('generalcleaning9_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning9_${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('generalcleaning9_' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning9_${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    // Page 10: Ground Floor
    function getPage10Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 2px solid #000; width: 100%; min-width: 1600px;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #6c757d; color: white; border: 1px solid #000; font-size: 16px; font-weight: bold; padding: 12px; vertical-align: middle; width: 100px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 300px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 1 - DATE: <input type="date" id="week1_date_p10" value="<?php echo e(now()->startOfWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    WEEK 2 - DATE: <input type="date" id="week2_date_p10" value="<?php echo e(now()->startOfWeek()->addWeek()->format('Y-m-d')); ?>" class="form-control d-inline-block" style="width: 160px; font-size: 14px; padding: 8px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 100px; background-color: #dc3545; color: white; border: 1px solid #000; font-size: 11px; padding: 4px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getGroundFloorTasksRows()}
                ${getGeneralCleaningRows10()}
              </tbody>
            </table>
        `;
    }

    function getGroundFloorTasksRows() {
        const groundFloorTasks = [
            'Wiped the elevator (wall, floor, and buttons).',
            'Swept the ground floor, stairs, CCTV area, and outside the PN Center.',
            'Properly arranged the receiving area and things on the ground floor (tables, water gallons, cabinets, etc.).',
            'Mopped the stairs and CCTV area (tiles).',
            'Thoroughly arranged the CCTV table, electric fan, and bench/chairs.',
            'Wiped the windows.',
            'Properly cleaned and brushed the comfort room.',
            'Ensured that the receiving area is well-maintained and organized.',
            'Wiped the wall outside, ensuring there are no visible stains.'
        ];

        let rows = '';
        groundFloorTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${groundFloorTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">GROUND<br>FLOOR<br><br>2-3<br>unchecked-<br>for<br>improvement<br>4 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('groundfloor10_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="groundfloor10_${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('groundfloor10_' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="groundfloor10_${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGeneralCleaningRows10() {
        const generalCleaningTasks = [
            'Wiped the cabinets, tables, and chairs.',
            'Brushed and rinsed the floor of the ground floor.',
            'Arrange the ground floor once the floor is dry.'
        ];

        let rows = '';
        generalCleaningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${generalCleaningTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">General<br>Cleaning</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('generalcleaning10_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning10_${index + 1}" data-week="1"></textarea>
                    </td>
                    ${generateCheckboxCells('generalcleaning10_' + (index + 1), 'week2')}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning10_${index + 1}" data-week="2"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    // Initialize with Page 1 content when modal opens
    document.addEventListener('DOMContentLoaded', function() {
        // Load page 1 content by default when modal is opened
        const taskModal = document.getElementById('taskChecklistModal');
        if (taskModal) {
            taskModal.addEventListener('shown.bs.modal', function() {
                loadTaskPageContent(currentTaskPage);
                document.getElementById('currentPageNumber').textContent = currentTaskPage;
            });
        }

        // Add event listeners for confirm buttons
        // Confirm Add Members button
        const confirmAddBtn = document.getElementById('confirmAddMembers');
        if (confirmAddBtn) {
          confirmAddBtn.addEventListener('click', function() {
            if (selectedStudentsToAdd.length === 0) {
              alert('Please select at least one student to add.');
              return;
            }

            const studentIds = selectedStudentsToAdd.map(s => s.id);

            fetch(`/assignments/category/${currentCategoryId}/add-members`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
              },
              body: JSON.stringify({
                student_ids: studentIds
              })
            })
            .then(response => response.json())
            .then(data => {
              if (data.success) {
                showNotification('success', data.message);
                // Close modal and refresh page
                bootstrap.Modal.getInstance(document.getElementById('addMembersModal')).hide();
                setTimeout(() => location.reload(), 1500);
              } else {
                alert('Error adding members: ' + data.message);
              }
            })
            .catch(error => {
              console.error('Error:', error);
              alert('Error adding members');
            });
          });
        }

        // Confirm Delete Members button (remove from category only)
        const confirmDeleteBtn = document.getElementById('confirmDeleteMembers');
        if (confirmDeleteBtn) {
          confirmDeleteBtn.addEventListener('click', function() {
            if (selectedMembersToDelete.length === 0) {
              alert('Please select at least one member to remove from category.');
              return;
            }

            const memberIds = selectedMembersToDelete.map(m => m.id);

            if (!confirm(`Are you sure you want to remove ${selectedMembersToDelete.length} member(s) from this category?`)) {
              return;
            }

            fetch(`/assignments/category/${currentCategoryId}/remove-members`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
              },
              body: JSON.stringify({
                member_ids: memberIds
              })
            })
            .then(response => response.json())
            .then(data => {
              if (data.success) {
                showNotification('success', data.message);
                // Close modal and refresh page
                bootstrap.Modal.getInstance(document.getElementById('deleteMembersModal')).hide();
                setTimeout(() => location.reload(), 1500);
              } else {
                alert('Error removing members: ' + data.message);
              }
            })
            .catch(error => {
              console.error('Error:', error);
              alert('Error removing members');
            });
          });
        }

        // Confirm Delete from System button
        const confirmDeleteFromSystemBtn = document.getElementById('confirmDeleteFromSystem');
        if (confirmDeleteFromSystemBtn) {
          confirmDeleteFromSystemBtn.addEventListener('click', function() {
            if (selectedStudentsToDeleteFromSystem.length === 0) {
              alert('Please select at least one student to delete from system.');
              return;
            }

            const studentIds = selectedStudentsToDeleteFromSystem.map(s => s.id);
            const studentNames = selectedStudentsToDeleteFromSystem.map(s => s.name).join(', ');

            if (!confirm(`Are you sure you want to PERMANENTLY DELETE these students from the entire system?\n\n${studentNames}\n\nThis action cannot be undone and will remove them from all assignments.`)) {
              return;
            }

            fetch('/students/delete-multiple', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
              },
              body: JSON.stringify({
                student_ids: studentIds
              })
            })
            .then(response => response.json())
            .then(data => {
              if (data.success) {
                showNotification('success', data.message);
                // Close modal and refresh page
                bootstrap.Modal.getInstance(document.getElementById('deleteMembersModal')).hide();
                setTimeout(() => location.reload(), 1500);
              } else {
                alert('Error deleting students: ' + data.message);
              }
            })
            .catch(error => {
              console.error('Error:', error);
              alert('Error deleting students');
            });
          });
        }
    });

    // Single checkbox functions
    function showOptions(cellId) {
        const mainBox = document.getElementById(cellId);
        const optionsDiv = document.getElementById(cellId + '_options');

        // Hide main box and show options
        mainBox.style.display = 'none';
        optionsDiv.style.display = 'flex';
        optionsDiv.style.gap = '2px';
        optionsDiv.style.justifyContent = 'center';
        optionsDiv.style.alignItems = 'center';
    }

    function selectOption(cellId, option) {
        const mainBox = document.getElementById(cellId);
        const optionsDiv = document.getElementById(cellId + '_options');

        // Hide options and show main box with selected option
        optionsDiv.style.display = 'none';
        mainBox.style.display = 'flex';

        if (option === 'check') {
            mainBox.style.backgroundColor = '#28a745';
            mainBox.style.color = 'white';
            mainBox.style.border = '2px solid #28a745';
            mainBox.innerHTML = '✓';
            mainBox.setAttribute('data-selected', 'check');
        } else if (option === 'wrong') {
            mainBox.style.backgroundColor = '#dc3545';
            mainBox.style.color = 'white';
            mainBox.style.border = '2px solid #dc3545';
            mainBox.innerHTML = '✗';
            mainBox.setAttribute('data-selected', 'wrong');
        }

        // Add click handler to reset if clicked again
        mainBox.onclick = function() {
            resetBox(cellId);
        };
    }

    function resetBox(cellId) {
        const mainBox = document.getElementById(cellId);

        // Reset to original state - completely empty
        mainBox.style.backgroundColor = 'white';
        mainBox.style.color = 'black';
        mainBox.style.border = '2px solid black';
        mainBox.innerHTML = '';
        mainBox.removeAttribute('data-selected');

        // Restore original click handler
        mainBox.onclick = function() {
            showOptions(cellId);
        };
    }
  </script>

  <style>
    /* Task Checklist Modal Styles - Enhanced Tabular Format */
    .table {
      border-collapse: collapse !important;
      margin: 0 !important;
      background-color: #ffffff !important;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    }

    /* Enhanced table headers */
    .table thead th {
      background-color: transparent !important;
      border: 1px solid #dee2e6 !important;
      font-weight: 600 !important;
      text-align: center !important;
      padding: 12px 8px !important;
      color: #333 !important;
      font-size: 13px !important;
    }

    /* Category headers with better styling */
    .table thead th.category-header {
      background-color: transparent !important;
      font-weight: 600 !important;
      font-size: 14px !important;
      color: #333 !important;
    }

    /* Week headers */
    .table thead th.week-header {
      background-color: transparent !important;
      font-weight: 600 !important;
      font-size: 13px !important;
      color: #333 !important;
    }

    /* Day headers */
    .table thead th.day-header {
      background-color: transparent !important;
      font-weight: 600 !important;
      font-size: 11px !important;
      color: #333 !important;
    }

    .table td, .table th {
      border: 1px solid #000 !important;
      padding: 2px !important;
      vertical-align: middle !important;
      line-height: 1.2 !important;
    }

    .status-buttons {
      display: flex !important;
      gap: 2px !important;
      justify-content: center !important;
      align-items: center !important;
    }

    .status-btn {
      width: 30px !important;
      height: 30px !important;
      border: 2px solid #000 !important;
      background: white !important;
      font-size: 14px !important;
      font-weight: bold !important;
      cursor: pointer !important;
      border-radius: 4px !important;
      padding: 0 !important;
      margin: 2px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      line-height: 1 !important;
      transition: all 0.2s ease !important;
    }

    .check-btn {
      background: white !important;
      color: #28a745 !important;
      border: 2px solid #28a745 !important;
      width: 30px !important;
      height: 30px !important;
      font-size: 14px !important;
      font-weight: bold !important;
    }

    .check-btn:hover {
      background: #e8f5e8 !important;
    }

    .check-btn.active {
      background: #28a745 !important;
      color: white !important;
      border: 2px solid #28a745 !important;
    }

    .wrong-btn {
      background: white !important;
      color: #dc3545 !important;
      border: 2px solid #dc3545 !important;
      width: 30px !important;
      height: 30px !important;
      font-size: 14px !important;
      font-weight: bold !important;
    }

    .wrong-btn:hover {
      background: #fdeaea !important;
    }

    .wrong-btn.active {
      background: #dc3545 !important;
      color: white !important;
      border: 2px solid #dc3545 !important;
    }

    .remarks-input {
      border: none !important;
      resize: none !important;
      background: #ffebee !important;
      padding: 4px !important;
      margin: 0 !important;
      outline: none !important;
      width: 100% !important;
      height: 30px !important;
      font-size: 11px !important;
      font-weight: 500 !important;
      line-height: 1.2 !important;
      color: #d32f2f !important;
    }

    .remarks-input:focus {
      box-shadow: none !important;
      border: none !important;
    }

    /* Make Task Checklist Modal Content Larger */
    #taskChecklistModal .modal-content {
      width: 98vw !important;
      max-width: 1800px !important;
      margin: 1rem 1rem 1rem 0.5rem !important;
      height: 95vh !important;
      overflow-x: hidden !important;
      overflow-y: auto !important;
    }

    #taskChecklistModal .modal-dialog {
      max-width: 98vw !important;
      width: 98vw !important;
      margin: 1rem 1rem 1rem 0.5rem !important;
      height: auto !important;
    }

    /* Improved Modal Table Spacing */
    .modal-body table {
      width: 100% !important;
      border-collapse: collapse !important;
      border-spacing: 0 !important;
      margin: 10px 0 !important;
      max-width: 1700px !important;
      font-size: 12px !important;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
      border: 2px solid #000 !important;
    }

    .modal-body table th,
    .modal-body table td {
      padding: 8px 6px !important;
      border: 2px solid #000 !important;
      text-align: center !important;
      vertical-align: middle !important;
      font-size: 12px !important;
      line-height: 1.4 !important;d
      font-weight: 500 !important;
    }

    .modal-body table th {
      background-color: #2c3e50 !important;
      font-weight: bold !important;
      color: white !important;
      border: 2px solid #000 !important;
      padding: 12px 8px !important;
      text-align: center !important;
      font-size: 13px !important;
      text-transform: uppercase !important;
      letter-spacing: 0.5px !important;
    }

    .modal-body table tbody tr:nth-child(even) {
      background-color: #f8f9fa !important;
    }

    .modal-body table tbody tr:nth-child(odd) {
      background-color: #ffffff !important;
    }

    .modal-body table tbody tr:hover {
      background-color: #e3f2fd !important;
    }

    /* Center the fullscreen modal content */
    .modal-fullscreen .modal-content {
      display: flex !important;
      flex-direction: column !important;
      justify-content: center !important;
      align-items: center !important;
      min-height: 100vh !important;
      margin: 0 auto !important;
      max-width: 95% !important;
      padding: 20px !important;
    }

    .modal-fullscreen .modal-body {
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      width: 100% !important;
      overflow: auto !important;
    }

    .modal-fullscreen .modal-header {
      width: 100% !important;
      text-align: center !important;
      justify-content: center !important;
      position: relative !important;
    }

    .modal-fullscreen .modal-header .btn-close {
      position: absolute !important;
      right: 20px !important;
      top: 50% !important;
      transform: translateY(-50%) !important;
    }

    /* Better Modal Centering */
    .modal-dialog {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      min-height: calc(100vh - 3.5rem) !important;
    }

    /* Specific adjustments for member modals */
    #studentAssignModal .modal-content,
    #addMembersModal .modal-content,
    #deleteMembersModal .modal-content,
    #editMembersModal .modal-content {
      transform: none !important;
      left: auto !important;
      margin: 0 auto !important;
    }

    /* Task description styling - Enhanced Tabular Format */
    .task-cell {
      background-color: #ffffff !important;
      padding: 12px 16px !important;
      border: 1px solid #333 !important;
      font-size: 14px !important;
      font-weight: normal !important;
      line-height: 1.5 !important;
      vertical-align: middle !important;
      width: 350px !important;
      white-space: normal !important;
      text-align: left !important;
      color: #333333 !important;
      border-right: 2px solid #666 !important;
    }

    /* Bold styling for important task items */
    .task-cell.important-task {
      font-weight: bold !important;
      background-color: #f8f9fa !important;
      color: #2c3e50 !important;
    }

    /* Room title styling */
    .task-cell.room-title {
      background-color: #fff3cd !important;
      font-weight: bold !important;
      color: #856404 !important;
      text-align: center !important;
      font-size: 15px !important;
    }

    /* Day cells - Enhanced Tabular Format */
    .day-cell {
      text-align: center !important;
      padding: 8px 6px !important;
      border: 1px solid #333 !important;
      width: 75px !important;
      height: 45px !important;
      vertical-align: middle !important;
      background-color: #ffffff !important;
      font-weight: normal !important;
    }

    /* Checkbox cells styling */
    .checkbox-cell {
      background-color: #fafafa !important;
      border: 1px solid #333 !important;
      padding: 6px !important;
      text-align: center !important;
      vertical-align: middle !important;
    }

    /* Single checkbox styling */
    .single-checkbox {
      border: 2px solid #333 !important;
      background-color: #ffffff !important;
      border-radius: 4px !important;
      transition: all 0.2s ease !important;
    }

    .single-checkbox:hover {
      border-color: #007bff !important;
      box-shadow: 0 0 0 2px rgba(0,123,255,0.25) !important;
    }

    /* Remarks cells - Enhanced Tabular Format */
    .remarks-cell {
      padding: 6px !important;
      border: 1px solid #333 !important;
      width: 120px !important;
      vertical-align: middle !important;
      background-color: #fff5f5 !important;
    }

    /* Category cells - Enhanced styling */
    .category-cell {
      border: 2px solid #333 !important;
      font-weight: bold !important;
      text-align: center !important;
      vertical-align: middle !important;
      padding: 12px 8px !important;
      line-height: 1.3 !important;
    }

    /* Remarks input styling */
    .remarks-input {
      width: 100% !important;
      height: 35px !important;
      border: 1px solid #ccc !important;
      border-radius: 3px !important;
      padding: 4px 6px !important;
      font-size: 11px !important;
      background-color: #ffffff !important;
      resize: vertical !important;
      font-family: Arial, sans-serif !important;
    }

    .remarks-input:focus {
      border-color: #007bff !important;
      box-shadow: 0 0 0 2px rgba(0,123,255,0.25) !important;
      outline: none !important;
    }

    /* Table row styling */
    .table tbody tr {
      border-bottom: 1px solid #dee2e6 !important;
    }

    .table tbody tr:nth-child(even) {
      background-color: #f8f9fa !important;
    }

    .table tbody tr:hover {
      background-color: #e3f2fd !important;
    }
  </style>
</body>
</html><?php /**PATH C:\Group16\resources\views/dashboard.blade.php ENDPATH**/ ?>