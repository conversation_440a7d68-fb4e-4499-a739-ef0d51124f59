<?php

require_once 'vendor/autoload.php';

// Load <PERSON>vel app
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

echo "=== Database Structure Verification ===\n\n";

// Check if tables exist
$tables = ['categories', 'students', 'assignments', 'assignment_members'];
foreach ($tables as $table) {
    if (Schema::hasTable($table)) {
        echo "✅ Table '{$table}' exists\n";
        
        // Get columns for assignment_members
        if ($table === 'assignment_members') {
            $columns = Schema::getColumnListing($table);
            echo "   Columns: " . implode(', ', $columns) . "\n";
            
            // Check specific columns
            $requiredColumns = ['comments', 'comment_created_at'];
            foreach ($requiredColumns as $column) {
                if (in_array($column, $columns)) {
                    echo "   ✅ Column '{$column}' exists\n";
                } else {
                    echo "   ❌ Column '{$column}' missing\n";
                }
            }
        }
        
        // Check students table gender enum
        if ($table === 'students') {
            try {
                $result = DB::select("SHOW COLUMNS FROM students WHERE Field = 'gender'");
                if (!empty($result)) {
                    echo "   Gender enum: " . $result[0]->Type . "\n";
                }
            } catch (Exception $e) {
                echo "   Error checking gender enum: " . $e->getMessage() . "\n";
            }
        }
    } else {
        echo "❌ Table '{$table}' missing\n";
    }
}

echo "\n=== Sample Data Check ===\n";

try {
    $studentCount = DB::table('students')->count();
    echo "Students in database: {$studentCount}\n";
    
    if ($studentCount > 0) {
        $genderCounts = DB::table('students')
            ->select('gender', DB::raw('count(*) as count'))
            ->groupBy('gender')
            ->get();
        
        foreach ($genderCounts as $gender) {
            echo "  {$gender->gender}: {$gender->count}\n";
        }
    }
} catch (Exception $e) {
    echo "Error checking student data: " . $e->getMessage() . "\n";
}

echo "\n=== Verification Complete ===\n";
