<?php
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\StudentController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\AssignmentController;
use App\Http\Controllers\BatchController;


use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

// Dashboard
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
Route::get('/task-checklist', [DashboardController::class, 'taskChecklist'])->name('task.checklist');
Route::post('/task-checklist/update-status', [DashboardController::class, 'updateTaskStatus'])->name('task.updateStatus');
Route::post('/task-checklist/update-remarks', [DashboardController::class, 'updateTaskRemarks'])->name('task.updateRemarks');
Route::post('/task-checklist/update-dates', [DashboardController::class, 'updateWeekDates'])->name('task.updateDates');
Route::get('/', function () { return redirect()->route('dashboard'); });

// Students
Route::get('/students', [StudentController::class, 'index'])->name('students.index');
Route::get('/students/create', [StudentController::class, 'create'])->name('students.create');
Route::post('/students', [StudentController::class, 'store'])->name('students.store');
Route::get('/student/{id}', [StudentController::class, 'show'])->name('student.show');
Route::delete('/students/remove/{name}', [StudentController::class, 'removeStudent'])->name('students.remove');

// Student Management (for View Members modal)
Route::put('/students/{id}/update-name', [StudentController::class, 'updateName'])->name('students.updateName');
Route::delete('/students/{id}', [StudentController::class, 'destroy'])->name('students.destroy');
Route::post('/students/quick-add', [StudentController::class, 'quickAdd'])->name('students.quickAdd');
Route::post('/students/quick-add-to-category', [StudentController::class, 'quickAddToCategory'])->name('students.quickAddToCategory');
Route::get('/students/all-for-deletion', [StudentController::class, 'getAllForDeletion'])->name('students.getAllForDeletion');
Route::post('/students/delete-multiple', [StudentController::class, 'deleteMultiple'])->name('students.deleteMultiple');

// Batch Management
Route::get('/batches', [BatchController::class, 'index'])->name('batches.index');
Route::post('/batches', [BatchController::class, 'store'])->name('batches.store');
Route::put('/batches/{batch}', [BatchController::class, 'update'])->name('batches.update');
Route::delete('/batches/{batch}', [BatchController::class, 'destroy'])->name('batches.destroy');
Route::get('/api/batches/active', [BatchController::class, 'getActiveBatches'])->name('batches.active');

// Categories
Route::get('/categories', [CategoryController::class, 'index'])->name('categories.index');
Route::post('/categories', [CategoryController::class, 'store'])->name('categories.store');
Route::delete('/categories/{category}', [CategoryController::class, 'destroy'])->name('categories.destroy');

// Assignments
Route::get('/assignments', [AssignmentController::class, 'index'])->name('assignments.index');
Route::get('/assignments/create', [AssignmentController::class, 'create'])->name('assignments.create');
Route::post('/assignments', [AssignmentController::class, 'store'])->name('assignments.store');
Route::post('/assignments/auto-shuffle', [AssignmentController::class, 'autoShuffle'])->name('assignments.autoShuffle');
Route::post('/assignments/cleanup-duplicates', [AssignmentController::class, 'cleanupDuplicates'])->name('assignments.cleanupDuplicates');
Route::get('/assignments/category/{categoryId}/members', [AssignmentController::class, 'getCategoryMembers'])->name('assignments.getCategoryMembers');
Route::post('/assignments/update-member-comment', [AssignmentController::class, 'updateMemberComment'])->name('assignments.updateMemberComment');
Route::get('/assignments/category/{categoryId}/available-students', [AssignmentController::class, 'getAvailableStudents'])->name('assignments.getAvailableStudents');
Route::get('/assignments/category/{categoryId}/current-members', [AssignmentController::class, 'getCurrentMembers'])->name('assignments.getCurrentMembers');
Route::post('/assignments/category/{categoryId}/add-members', [AssignmentController::class, 'addMembers'])->name('assignments.addMembers');
Route::post('/assignments/category/{categoryId}/remove-members', [AssignmentController::class, 'removeMembers'])->name('assignments.removeMembers');





// Logout
Route::post('/logout', function () {
    Auth::logout();
    return redirect('/'); // Redirect to homepage or login page
})->name('logout');