<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\AssignmentMember;

class CleanExpiredComments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'comments:clean-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean expired member comments (older than 1 day)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Cleaning expired comments...');
        
        $cleanedCount = AssignmentMember::cleanExpiredComments();
        
        $this->info("Cleaned {$cleanedCount} expired comments.");
        
        return Command::SUCCESS;
    }
}
