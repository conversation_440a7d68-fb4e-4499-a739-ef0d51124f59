<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if columns exist before adding them
        if (!Schema::hasColumn('assignment_members', 'comments')) {
            Schema::table('assignment_members', function (Blueprint $table) {
                $table->text('comments')->nullable()->after('is_coordinator');
            });
        }

        if (!Schema::hasColumn('assignment_members', 'comment_created_at')) {
            Schema::table('assignment_members', function (Blueprint $table) {
                $table->timestamp('comment_created_at')->nullable()->after('comments');
            });
        }

        // Check if assignments table has status column
        if (!Schema::hasColumn('assignments', 'status')) {
            Schema::table('assignments', function (Blueprint $table) {
                $table->string('status')->default('current')->after('end_date');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assignment_members', function (Blueprint $table) {
            if (Schema::hasColumn('assignment_members', 'comment_created_at')) {
                $table->dropColumn('comment_created_at');
            }
            if (Schema::hasColumn('assignment_members', 'comments')) {
                $table->dropColumn('comments');
            }
        });

        Schema::table('assignments', function (Blueprint $table) {
            if (Schema::hasColumn('assignments', 'status')) {
                $table->dropColumn('status');
            }
        });
    }
};
