<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Student extends Model
{
    protected $fillable = [
        'name',
        'gender',
        'batch', // Use 'batch' instead of 'class_year' for consistency with your DB
    ];

    public function assignmentMembers()
    {
        return $this->hasMany(AssignmentMember::class);
    }

    public function batchModel()
    {
        return $this->belongsTo(Batch::class, 'batch', 'year');
    }

    // Get available batches for dropdowns (all active batches, even without students)
    public static function getAvailableBatches()
    {
        return \App\Models\Batch::active()->pluck('year')->toArray();
    }

    // Get batches that have students (for display purposes)
    public static function getBatchesWithStudents()
    {
        return \App\Models\Batch::active()
            ->whereHas('students')
            ->get();
    }
}