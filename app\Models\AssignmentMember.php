<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssignmentMember extends Model
{
    use HasFactory;

    protected $fillable = [
        'assignment_id',
        'student_id',
        'is_coordinator',
        'comments',
        'comment_created_at',
    ];

    protected $dates = [
        'comment_created_at',
    ];

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function assignment()
    {
        return $this->belongsTo(Assignment::class);
    }

    // Check if comment is expired (older than 1 day)
    public function isCommentExpired()
    {
        if (!$this->comment_created_at || !$this->comments) {
            return false;
        }

        return $this->comment_created_at->diffInDays(now()) >= 1;
    }

    // Clean expired comments
    public static function cleanExpiredComments()
    {
        $expiredMembers = self::where('comments', '!=', null)
            ->where('comment_created_at', '<=', now()->subDay())
            ->get();

        foreach ($expiredMembers as $member) {
            $member->update([
                'comments' => null,
                'comment_created_at' => null
            ]);
        }

        return $expiredMembers->count();
    }
}