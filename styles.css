* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

:root {
    --primary-color: #4A90E2;
    --secondary-color: #F5F7FA;
    --text-color: #2C3E50;
    --border-color: #E1E4E8;
    --hover-color: #F0F2F5;
    --active-color: #3A80D2;
}

body {
    background-color: var(--secondary-color);
    color: var(--text-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color);
}

header h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
}

.user-profile {
    font-size: 24px;
    color: var(--primary-color);
    cursor: pointer;
    transition: color 0.3s ease;
}

.user-profile:hover {
    color: var(--active-color);
}

main {
    display: flex;
    margin-top: 20px;
    gap: 20px;
}

.sidebar {
    width: 250px;
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 20px;
}

.sidebar nav ul {
    list-style: none;
}

.sidebar nav ul li {
    padding: 12px;
    margin-bottom: 5px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.sidebar nav ul li:hover {
    background-color: var(--hover-color);
}

.sidebar nav ul li.active {
    background-color: var(--primary-color);
    color: white;
}

.sidebar nav ul li i {
    width: 20px;
}

.content {
    flex: 1;
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.task-header h2 {
    font-size: 20px;
    font-weight: 500;
}

.add-task-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s ease;
}

.add-task-btn:hover {
    background-color: var(--active-color);
}

.task-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.search-box {
    flex: 1;
    position: relative;
}

.search-box input {
    width: 100%;
    padding: 10px 15px 10px 35px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    outline: none;
    transition: border-color 0.3s ease;
}

.search-box input:focus {
    border-color: var(--primary-color);
}

.search-box i {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.filter-options select {
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    outline: none;
    background-color: white;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.filter-options select:focus {
    border-color: var(--primary-color);
}

.task-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    main {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        margin-bottom: 20px;
    }

    .task-filters {
        flex-direction: column;
    }

    .task-header h2 {
        font-size: 18px;
    }

    .add-task-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 20px;
    }

    .user-profile {
        font-size: 20px;
    }

    .task-header h2 {
        font-size: 16px;
    }
}
