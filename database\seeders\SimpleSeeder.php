<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SimpleSeeder extends Seeder
{
    public function run(): void
    {
        $now = Carbon::now();

        // 1. Categories
        $categories = [
            'Kitchen',
            'Dishwashing', 
            'Dining',
            'Offices & Conference Rooms',
            'Garbage, Rugs, & Rooftop',
            'Ground Floor',
        ];
        
        foreach ($categories as $cat) {
            DB::table('categories')->updateOrInsert([
                'name' => $cat
            ], [
                'created_at' => $now,
                'updated_at' => $now
            ]);
        }

        // 2. Sample students from your database
        $batch2025_females = [
            '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>, <PERSON><PERSON>', '<PERSON><PERSON>-as <PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>, <PERSON>', '<PERSON><PERSON><PERSON>, <PERSON>', '<PERSON><PERSON>, <PERSON>',
            '<PERSON><PERSON>-<PERSON><PERSON>, <PERSON>', '<PERSON><PERSON>, <PERSON><PERSON><PERSON>', '<PERSON><PERSON>, <PERSON>',
            '<PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>, <PERSON><PERSON>'
        ];

        $batch2025_males = [
            '<PERSON><PERSON><PERSON>, <PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>, <PERSON>', '<PERSON><PERSON><PERSON>, <PERSON>',
            '<PERSON><PERSON><PERSON>, <PERSON><PERSON>', '<PERSON><PERSON><PERSON>, <PERSON><PERSON>', '<PERSON><PERSON>, <PERSON>',
            '<PERSON><PERSON><PERSON>, <PERSON>', '<PERSON><PERSON><PERSON><PERSON>, <PERSON>', '<PERSON>, <PERSON>',
            '<PERSON>, <PERSON>', '<PERSON><PERSON>, <PERSON>', '<PERSON>, <PERSON><PERSON>'
        ];

        <PERSON>batch20<PERSON>_<PERSON> = [
            '<PERSON><PERSON>, <PERSON><PERSON> <PERSON>', '<PERSON><PERSON>, <PERSON>', '<PERSON><PERSON>, <PERSON> <PERSON><PERSON>',
            '<PERSON>, <PERSON> <PERSON>', '<PERSON>, <PERSON>', '<PERSON>, <PERSON>',
            '<PERSON>, <PERSON><PERSON> <PERSON>', 'Lumayaga, Allysa Joyce', 'Barro, Niña Kathleen',
            'Gesim, Jella Mae', 'Judico, Kristel Jean', 'Bawic, Mariel Ann'
        ];

        $batch2026_males = [
            'Engaña, Riel Jake', 'Orozco, John Paul', 'Arellano, Vinzon',
            'Goles, Julius', 'Solon, Bryle', 'Mansanades, John Michael',
            'Ruales, Klint', 'Sabandal, Milven', 'Pacunla, Carlo',
            'Vicaran, Edemar', 'Navarro, Kent John', 'Cernal, Vhenz'
        ];

        // Insert students
        foreach ($batch2025_females as $name) {
            DB::table('students')->updateOrInsert([
                'name' => $name,
                'batch' => 2025
            ], [
                'gender' => 'Female',
                'created_at' => $now,
                'updated_at' => $now
            ]);
        }

        foreach ($batch2025_males as $name) {
            DB::table('students')->updateOrInsert([
                'name' => $name,
                'batch' => 2025
            ], [
                'gender' => 'Male',
                'created_at' => $now,
                'updated_at' => $now
            ]);
        }

        foreach ($batch2026_females as $name) {
            DB::table('students')->updateOrInsert([
                'name' => $name,
                'batch' => 2026
            ], [
                'gender' => 'Female',
                'created_at' => $now,
                'updated_at' => $now
            ]);
        }

        foreach ($batch2026_males as $name) {
            DB::table('students')->updateOrInsert([
                'name' => $name,
                'batch' => 2026
            ], [
                'gender' => 'Male',
                'created_at' => $now,
                'updated_at' => $now
            ]);
        }

        echo "Seeded " . count($batch2025_females) . " Batch 2025 females\n";
        echo "Seeded " . count($batch2025_males) . " Batch 2025 males\n";
        echo "Seeded " . count($batch2026_females) . " Batch 2026 females\n";
        echo "Seeded " . count($batch2026_males) . " Batch 2026 males\n";
    }
}
