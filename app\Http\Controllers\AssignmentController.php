<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Assignment;
use App\Models\Category;
use App\Models\Student;
use App\Models\AssignmentMember;
use App\Models\CategoryLimit;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AssignmentController extends Controller
{
    // Show all assignments with related data
    public function index()
    {
        $assignments = Assignment::with(['category', 'assignmentMembers.student'])->get();
        return view('assignments.index', compact('assignments'));
    }

    // Show all categories
    public function categories()
    {
        return response()->json(Category::all());
    }

    // Show all students
    public function students()
    {
        return response()->json(Student::all());
    }

    // Store a new assignment with members
    public function store(Request $request)
    {
        $request->validate([
            'category_id' => 'required|exists:categories,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date',
            'members' => 'required|array',
        ]);

        $assignment = Assignment::create([
            'category_id' => $request->category_id,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
        ]);

        foreach ($request->members as $student_id) {
            AssignmentMember::create([
                'assignment_id' => $assignment->id,
                'student_id' => $student_id,
                'is_coordinator' => false,
            ]);
        }

        return redirect('/assignments');
    }

    // Show a single assignment
    public function show($id)
    {
        $assignment = Assignment::with(['category', 'assignmentMembers.student'])->findOrFail($id);
        return response()->json($assignment);
    }

    // Delete an assignment
    public function destroy($id)
    {
        $assignment = Assignment::findOrFail($id);
        $assignment->delete();
        return response()->json(['success' => true]);
    }

    public function autoShuffle()
    {
        // Prevent multiple simultaneous auto-shuffles
        $lockFile = storage_path('app/auto_shuffle.lock');
        if (file_exists($lockFile)) {
            return redirect()->route('dashboard')->with('error', 'Auto-shuffle is already in progress. Please wait.');
        }

        // Create lock file
        file_put_contents($lockFile, time());

        try {
            // REMOVE 7-day restriction for testing - you can shuffle anytime now
            // Get the latest assignment end date
            // $latestAssignment = Assignment::orderBy('end_date', 'desc')->first();
            // $now = Carbon::now();
            // if ($latestAssignment && $now->lessThan(Carbon::parse($latestAssignment->end_date))) {
            //     // Not yet 7 days since last shuffle
            //     return redirect()->route('dashboard')->with('error', 'Auto-shuffle is only allowed once every 7 days. Next shuffle: ' . Carbon::parse($latestAssignment->end_date)->toFormattedDateString());
            // }

            $now = Carbon::now();

        // Move current assignments to previous with updated end date
        $currentAssignments = Assignment::where('status', 'current')->get();
        foreach ($currentAssignments as $assignment) {
            $assignment->update([
                'status' => 'previous',
                'end_date' => $now->copy()->subDay()->toDateString() // Set end date to yesterday
            ]);
        }

        // Get previous assignments to avoid reassigning students to same tasks
        $previousAssignments = Assignment::where('status', 'previous')->with('assignmentMembers.student')->get();
        $previousTaskAssignments = [];

        foreach ($previousAssignments as $assignment) {
            foreach ($assignment->assignmentMembers as $member) {
                $previousTaskAssignments[$member->student_id] = $assignment->category_id;
            }
        }



        $categories = Category::all();
        $students = Student::all();

        // Ensure categories are processed in a specific order to guarantee all get members
        $categoryOrder = [
            'Kitchen',
            'Dishwashing',
            'Dining',
            'Ground Floor',
            'Offices & Conference Rooms',
            'Garbage, Rugs, & Rooftop'
        ];

        $orderedCategories = collect();
        foreach ($categoryOrder as $categoryName) {
            $category = $categories->where('name', $categoryName)->first();
            if ($category) {
                $orderedCategories->push($category);
            }
        }
        $categories = $orderedCategories;

        // Define member limits per category
        $categoryLimits = [
            'Kitchen' => 32,
            'Ground Floor' => 18,
            'Dining' => 20,
            'Dishwashing' => 20,
            'Offices & Conference Rooms' => 20,
            'Garbage, Rugs, & Rooftop' => 20,
        ];

        // BULLETPROOF APPROACH: Pre-assign all students to categories
        $students2025 = $students->where('batch', 2025)->shuffle()->values();
        $students2026 = $students->where('batch', 2026)->shuffle()->values();

        // Create a pool of available students
        $availableStudents = collect();

        // Add all students to available pool with batch info
        foreach ($students2025 as $student) {
            $availableStudents->push($student);
        }
        foreach ($students2026 as $student) {
            $availableStudents->push($student);
        }

        // Shuffle the entire pool
        $availableStudents = $availableStudents->shuffle();

        // Track which students have been assigned
        $usedStudentIds = [];

        // Calculate total students needed
        $totalNeeded = array_sum($categoryLimits);
        $totalAvailable = $availableStudents->count();

        \Log::info("Total students available: {$totalAvailable}, Total needed: {$totalNeeded}");

        foreach ($categories as $category) {
            // Get member limit for this category
            $maxTotal = $categoryLimits[$category->name] ?? 20;

            // Debug: Log category processing
            $remainingStudents = $availableStudents->count() - count($usedStudentIds);
            \Log::info("Processing category: {$category->name}, Max: {$maxTotal}, Remaining students: {$remainingStudents}");

            $selected2025 = collect();
            $selected2026 = collect();

            // SPECIAL HANDLING FOR KITCHEN: 20 boys + 12 girls with equal batch distribution
            if ($category->name === 'Kitchen') {
                // Kitchen needs: 10 boys + 6 girls per batch = 16 per batch = 32 total
                $boys2025Needed = 10;
                $girls2025Needed = 6;
                $boys2026Needed = 10;
                $girls2026Needed = 6;

                $boys2025Found = 0;
                $girls2025Found = 0;
                $boys2026Found = 0;
                $girls2026Found = 0;

                // Get boys for batch 2025
                foreach ($availableStudents as $student) {
                    if (in_array($student->id, $usedStudentIds)) continue;

                    // Skip if student was assigned to this category in previous week
                    if (isset($previousTaskAssignments[$student->id]) && $previousTaskAssignments[$student->id] == $category->id) {
                        continue;
                    }

                    if ($student->batch == 2025 && $student->gender === 'Male' && $boys2025Found < $boys2025Needed) {
                        $selected2025->push($student);
                        $usedStudentIds[] = $student->id;
                        $boys2025Found++;
                    }
                }

                // Get girls for batch 2025
                foreach ($availableStudents as $student) {
                    if (in_array($student->id, $usedStudentIds)) continue;

                    // Skip if student was assigned to this category in previous week
                    if (isset($previousTaskAssignments[$student->id]) && $previousTaskAssignments[$student->id] == $category->id) {
                        continue;
                    }

                    if ($student->batch == 2025 && $student->gender === 'Female' && $girls2025Found < $girls2025Needed) {
                        $selected2025->push($student);
                        $usedStudentIds[] = $student->id;
                        $girls2025Found++;
                    }
                }

                // Get boys for batch 2026
                foreach ($availableStudents as $student) {
                    if (in_array($student->id, $usedStudentIds)) continue;

                    // Skip if student was assigned to this category in previous week
                    if (isset($previousTaskAssignments[$student->id]) && $previousTaskAssignments[$student->id] == $category->id) {
                        continue;
                    }

                    if ($student->batch == 2026 && $student->gender === 'Male' && $boys2026Found < $boys2026Needed) {
                        $selected2026->push($student);
                        $usedStudentIds[] = $student->id;
                        $boys2026Found++;
                    }
                }

                // Get girls for batch 2026
                foreach ($availableStudents as $student) {
                    if (in_array($student->id, $usedStudentIds)) continue;

                    // Skip if student was assigned to this category in previous week
                    if (isset($previousTaskAssignments[$student->id]) && $previousTaskAssignments[$student->id] == $category->id) {
                        continue;
                    }

                    if ($student->batch == 2026 && $student->gender === 'Female' && $girls2026Found < $girls2026Needed) {
                        $selected2026->push($student);
                        $usedStudentIds[] = $student->id;
                        $girls2026Found++;
                    }
                }

                $allSelected = $selected2025->concat($selected2026);
            }
            // SPECIAL HANDLING FOR DISHWASHING: Include boys, equal batch distribution
            else if ($category->name === 'Dishwashing') {
                // Dishwashing: 10 per batch = 20 total, mixed boys and girls
                $needed2025 = 10;
                $needed2026 = 10;

                // Get students for batch 2025 (mixed gender)
                foreach ($availableStudents as $student) {
                    if (in_array($student->id, $usedStudentIds)) continue;

                    // Skip if student was assigned to this category in previous week
                    if (isset($previousTaskAssignments[$student->id]) && $previousTaskAssignments[$student->id] == $category->id) {
                        continue;
                    }

                    if ($student->batch == 2025 && $selected2025->count() < $needed2025) {
                        $selected2025->push($student);
                        $usedStudentIds[] = $student->id;
                    }
                }

                // Get students for batch 2026 (mixed gender)
                foreach ($availableStudents as $student) {
                    if (in_array($student->id, $usedStudentIds)) continue;

                    // Skip if student was assigned to this category in previous week
                    if (isset($previousTaskAssignments[$student->id]) && $previousTaskAssignments[$student->id] == $category->id) {
                        continue;
                    }

                    if ($student->batch == 2026 && $selected2026->count() < $needed2026) {
                        $selected2026->push($student);
                        $usedStudentIds[] = $student->id;
                    }
                }

                $allSelected = $selected2025->concat($selected2026);
            }
            // REGULAR HANDLING FOR OTHER CATEGORIES: Equal distribution by batch
            else {
                // Calculate equal distribution between batches
                $membersPerBatch = floor($maxTotal / 2);
                $remainder = $maxTotal % 2;
                $needed2025 = $membersPerBatch + ($remainder > 0 ? 1 : 0);
                $needed2026 = $membersPerBatch;

                // Get students for batch 2025
                foreach ($availableStudents as $student) {
                    if (in_array($student->id, $usedStudentIds)) continue;

                    // Skip if student was assigned to this category in previous week
                    if (isset($previousTaskAssignments[$student->id]) && $previousTaskAssignments[$student->id] == $category->id) {
                        continue;
                    }

                    if ($student->batch == 2025 && $selected2025->count() < $needed2025) {
                        $selected2025->push($student);
                        $usedStudentIds[] = $student->id;
                    }
                }

                // Get students for batch 2026
                foreach ($availableStudents as $student) {
                    if (in_array($student->id, $usedStudentIds)) continue;

                    // Skip if student was assigned to this category in previous week
                    if (isset($previousTaskAssignments[$student->id]) && $previousTaskAssignments[$student->id] == $category->id) {
                        continue;
                    }

                    if ($student->batch == 2026 && $selected2026->count() < $needed2026) {
                        $selected2026->push($student);
                        $usedStudentIds[] = $student->id;
                    }
                }

                // If we still need more students, fill from any available batch (including those from previous week if necessary)
                $totalSelected = $selected2025->count() + $selected2026->count();
                if ($totalSelected < $maxTotal) {
                    // First try to fill with students who weren't in this category last week
                    foreach ($availableStudents as $student) {
                        if (in_array($student->id, $usedStudentIds)) continue;

                        // Skip if student was assigned to this category in previous week
                        if (isset($previousTaskAssignments[$student->id]) && $previousTaskAssignments[$student->id] == $category->id) {
                            continue;
                        }

                        if ($totalSelected < $maxTotal) {
                            if ($student->batch == 2025) {
                                $selected2025->push($student);
                            } else {
                                $selected2026->push($student);
                            }
                            $usedStudentIds[] = $student->id;
                            $totalSelected++;
                        }
                    }

                    // If still not enough, allow students from previous week as last resort
                    if ($totalSelected < $maxTotal) {
                        foreach ($availableStudents as $student) {
                            if (in_array($student->id, $usedStudentIds)) continue;

                            if ($totalSelected < $maxTotal) {
                                if ($student->batch == 2025) {
                                    $selected2025->push($student);
                                } else {
                                    $selected2026->push($student);
                                }
                                $usedStudentIds[] = $student->id;
                                $totalSelected++;
                            }
                        }
                    }
                }

                $allSelected = $selected2025->concat($selected2026);
            }

            // Skip if we don't have enough students
            if ($allSelected->count() == 0) {
                \Log::warning("No students selected for category: {$category->name}");
                continue;
            }

            \Log::info("Selected {$allSelected->count()} students for {$category->name}: Batch 2025: {$selected2025->count()}, Batch 2026: {$selected2026->count()}");

            // Create assignment
            $assignment = Assignment::create([
                'category_id' => $category->id,
                'start_date' => $now->toDateString(),
                'end_date' => $now->copy()->addDays(7)->toDateString(),
                'status' => 'current'
            ]);

            \Log::info("Created new current assignment for category: {$category->name} with {$allSelected->count()} members");

            // Select coordinators (one from each batch if possible)
            $coor2025 = $selected2025->isNotEmpty() ? $selected2025->random() : null;
            $coor2026 = $selected2026->isNotEmpty() ? $selected2026->random() : null;

            \Log::info("Selected coordinators for {$category->name}: 2025=" . ($coor2025 ? $coor2025->name : 'none') . ", 2026=" . ($coor2026 ? $coor2026->name : 'none'));

            // Create assignment members
            foreach ($allSelected as $member) {
                $isCoordinator = false;
                if ($coor2025 && $member->id == $coor2025->id) {
                    $isCoordinator = true;
                }
                if ($coor2026 && $member->id == $coor2026->id) {
                    $isCoordinator = true;
                }

                AssignmentMember::create([
                    'assignment_id' => $assignment->id,
                    'student_id' => $member->id,
                    'is_coordinator' => $isCoordinator
                ]);

                \Log::info("Assigned {$member->name} to {$category->name}" . ($isCoordinator ? " (COORDINATOR)" : ""));
            }
        }

        // Debug: Check for duplicates and verify coordinators
        $allAssignedIds = [];
        $duplicateFound = false;
        $coordinatorCount = 0;

        $currentAssignments = Assignment::where('status', 'current')->with('assignmentMembers.student')->get();

        foreach ($currentAssignments as $assignment) {
            foreach ($assignment->assignmentMembers as $member) {
                if (in_array($member->student_id, $allAssignedIds)) {
                    $duplicateFound = true;
                    break 2;
                }
                $allAssignedIds[] = $member->student_id;

                if ($member->is_coordinator) {
                    $coordinatorCount++;
                }
            }
        }

        \Log::info("Final verification: {$coordinatorCount} coordinators assigned across all categories");

        $message = $duplicateFound ?
            'Auto-shuffle complete but duplicates detected! Please try again.' :
            "Auto-shuffle complete! All students assigned with NO DUPLICATES - each name appears only once. {$coordinatorCount} coordinators assigned. Previous assignments moved to history and students avoided same tasks where possible.";

        return redirect()->route('dashboard')->with('success', $message);

        } finally {
            // Remove lock file
            $lockFile = storage_path('app/auto_shuffle.lock');
            if (file_exists($lockFile)) {
                unlink($lockFile);
            }
        }
    }

    public function cleanupDuplicates()
    {
        // Clean up duplicate assignments
        $duplicates = Assignment::select('category_id', 'start_date', 'status')
            ->groupBy('category_id', 'start_date', 'status')
            ->havingRaw('COUNT(*) > 1')
            ->get();

        $deletedCount = 0;
        foreach ($duplicates as $duplicate) {
            $assignments = Assignment::where('category_id', $duplicate->category_id)
                ->where('start_date', $duplicate->start_date)
                ->where('status', $duplicate->status)
                ->orderBy('id', 'desc')
                ->get();

            // Keep the first (latest) one, delete the rest
            foreach ($assignments->skip(1) as $assignment) {
                $assignment->assignmentMembers()->delete();
                $assignment->delete();
                $deletedCount++;
            }
        }

        return redirect()->route('dashboard')->with('success', "Cleaned up {$deletedCount} duplicate assignments.");
    }

    public function create()
    {
        // Get categories and students for the assignment form
        $categories = \App\Models\Category::all();
        $students = \App\Models\Student::all();
        return view('assignments.create', compact('categories', 'students'));
    }

    // Get category members for editing
    public function getCategoryMembers($categoryId)
    {
        // Clean expired comments first
        AssignmentMember::cleanExpiredComments();

        // ONLY get CURRENT assignments, not previous ones
        $category = Category::with(['assignments' => function($query) {
            $query->where('status', 'current');
        }, 'assignments.assignmentMembers.student'])->findOrFail($categoryId);

        $members2025 = [];
        $members2026 = [];

        foreach($category->assignments as $assignment) {
            // Skip if not current assignment
            if ($assignment->status !== 'current') {
                continue;
            }

            foreach($assignment->assignmentMembers as $member) {
                // Check if comment is expired and clean it
                if ($member->isCommentExpired()) {
                    $member->update([
                        'comments' => null,
                        'comment_created_at' => null
                    ]);
                    $member->refresh();
                }

                if($member->student->batch == 2025) {
                    $members2025[] = $member;
                }
                if($member->student->batch == 2026) {
                    $members2026[] = $member;
                }
            }
        }

        return response()->json([
            'success' => true,
            'category_name' => $category->name,
            'members2025' => $members2025,
            'members2026' => $members2026
        ]);
    }

    // Get available students for adding to category
    public function getAvailableStudents($categoryId)
    {
        $category = Category::findOrFail($categoryId);

        // Get all students
        $allStudents = Student::all();

        // Get currently assigned students for this category
        $currentAssignment = Assignment::where('category_id', $categoryId)
            ->where('status', 'current')
            ->first();

        $assignedStudentIds = [];
        if ($currentAssignment) {
            $assignedStudentIds = AssignmentMember::where('assignment_id', $currentAssignment->id)
                ->pluck('student_id')
                ->toArray();
        }

        // Filter out already assigned students
        $availableStudents = $allStudents->whereNotIn('id', $assignedStudentIds);

        $students2025 = $availableStudents->where('batch', 2025)->values();
        $students2026 = $availableStudents->where('batch', 2026)->values();

        return response()->json([
            'success' => true,
            'students2025' => $students2025,
            'students2026' => $students2026
        ]);
    }

    // Get current members for deleting from category
    public function getCurrentMembers($categoryId)
    {
        $category = Category::with(['assignments' => function($query) {
            $query->where('status', 'current');
        }, 'assignments.assignmentMembers.student'])->findOrFail($categoryId);

        $members2025 = [];
        $members2026 = [];

        foreach($category->assignments as $assignment) {
            if ($assignment->status !== 'current') {
                continue;
            }

            foreach($assignment->assignmentMembers as $member) {
                if($member->student->batch == 2025) {
                    $members2025[] = $member;
                }
                if($member->student->batch == 2026) {
                    $members2026[] = $member;
                }
            }
        }

        return response()->json([
            'success' => true,
            'category_name' => $category->name,
            'members2025' => $members2025,
            'members2026' => $members2026
        ]);
    }

    // Add members to category
    public function addMembers(Request $request, $categoryId)
    {
        $request->validate([
            'student_ids' => 'required|array',
            'student_ids.*' => 'exists:students,id'
        ]);

        $category = Category::findOrFail($categoryId);

        // Get current assignment for this category
        $currentAssignment = Assignment::where('category_id', $categoryId)
            ->where('status', 'current')
            ->first();

        if (!$currentAssignment) {
            return response()->json([
                'success' => false,
                'message' => 'No current assignment found for this category.'
            ]);
        }

        // Check if students are already assigned
        $alreadyAssigned = AssignmentMember::where('assignment_id', $currentAssignment->id)
            ->whereIn('student_id', $request->student_ids)
            ->exists();

        if ($alreadyAssigned) {
            return response()->json([
                'success' => false,
                'message' => 'One or more students are already assigned to this category.'
            ]);
        }

        // Add the students
        foreach ($request->student_ids as $studentId) {
            AssignmentMember::create([
                'assignment_id' => $currentAssignment->id,
                'student_id' => $studentId,
                'is_coordinator' => false
            ]);
        }

        $studentCount = count($request->student_ids);
        return response()->json([
            'success' => true,
            'message' => "Successfully added {$studentCount} student(s) to {$category->name}."
        ]);
    }

    // Remove members from category
    public function removeMembers(Request $request, $categoryId)
    {
        $request->validate([
            'member_ids' => 'required|array',
            'member_ids.*' => 'exists:assignment_members,id'
        ]);

        $category = Category::findOrFail($categoryId);

        // Verify that all member IDs belong to the current assignment of this category
        $currentAssignment = Assignment::where('category_id', $categoryId)
            ->where('status', 'current')
            ->first();

        if (!$currentAssignment) {
            return response()->json([
                'success' => false,
                'message' => 'No current assignment found for this category.'
            ]);
        }

        $validMemberIds = AssignmentMember::where('assignment_id', $currentAssignment->id)
            ->whereIn('id', $request->member_ids)
            ->pluck('id')
            ->toArray();

        if (count($validMemberIds) !== count($request->member_ids)) {
            return response()->json([
                'success' => false,
                'message' => 'One or more members do not belong to this category.'
            ]);
        }

        // Remove the members
        AssignmentMember::whereIn('id', $request->member_ids)->delete();

        $memberCount = count($request->member_ids);
        return response()->json([
            'success' => true,
            'message' => "Successfully removed {$memberCount} member(s) from {$category->name}."
        ]);
    }

    // Update member comments
    public function updateMemberComment(Request $request)
    {
        $request->validate([
            'member_id' => 'required|exists:assignment_members,id',
            'comments' => 'nullable|string|max:500',
        ]);

        $member = AssignmentMember::findOrFail($request->member_id);

        // Check if comment is expired and clean it first
        if ($member->isCommentExpired()) {
            $member->update([
                'comments' => null,
                'comment_created_at' => null
            ]);
        }

        // Update with new comment and timestamp
        $updateData = [
            'comments' => $request->comments
        ];

        // Set timestamp only if comment is not empty
        if (!empty(trim($request->comments))) {
            $updateData['comment_created_at'] = now();
        } else {
            $updateData['comment_created_at'] = null;
        }

        $member->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Comment updated successfully',
            'comments' => $member->comments,
            'comment_created_at' => $member->comment_created_at
        ]);
    }
}