<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Student;

class StudentController extends Controller
{
    public function index() {
        $students = Student::orderBy('batch')->orderBy('name')->get();
        $batches = \App\Models\Batch::ordered()->get();
        $activeBatches = \App\Models\Batch::active()->get();
        return view('students.index', compact('students', 'batches', 'activeBatches'));
    }

    public function create() {
        return view('students.create');
    }

    public function store(Request $request) {
        $request->validate([
            'name' => 'required',
            'gender' => 'required|in:Male,Female',
            'batch' => 'required|integer',
        ]);
        Student::create($request->all());
        return redirect('/students');
    }

    public function show($id)
    {
        return "Student details for ID: " . $id;
    }

    public function dashboard()
    {
        return view('dashboard');
    }

    public function assignNewTasks()
    {
        return "Assign new tasks page";
    }

    public function shuffleTasks()
    {
        return "Shuffle tasks page";
    }

    // Remove a specific student from all assignments and database
    public function removeStudent($name)
    {
        $student = Student::where('name', $name)->first();

        if ($student) {
            // Remove from all assignment_members first (due to foreign key constraints)
            $student->assignmentMembers()->delete();

            // Then delete the student
            $student->delete();

            return response()->json(['success' => true, 'message' => "Student {$name} removed successfully"]);
        }

        return response()->json(['success' => false, 'message' => "Student {$name} not found"]);
    }

    // Update student name
    public function updateName(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $student = Student::findOrFail($id);
        $oldName = $student->name;
        $student->name = $request->name;
        $student->save();

        return response()->json([
            'success' => true,
            'message' => "Student name updated from '{$oldName}' to '{$student->name}'",
            'student' => $student
        ]);
    }

    // Delete student by ID
    public function destroy($id)
    {
        $student = Student::findOrFail($id);
        $studentName = $student->name;

        // Remove from all assignment_members first (due to foreign key constraints)
        $student->assignmentMembers()->delete();

        // Then delete the student
        $student->delete();

        return response()->json([
            'success' => true,
            'message' => "Student '{$studentName}' deleted successfully"
        ]);
    }

    // Quick add student (for View Members modal)
    public function quickAdd(Request $request)
    {
        $availableBatches = Student::getAvailableBatches();

        $request->validate([
            'name' => 'required|string|max:255',
            'gender' => 'required|in:Male,Female',
            'batch' => 'required|integer|in:' . implode(',', $availableBatches),
        ]);

        $student = Student::create([
            'name' => $request->name,
            'gender' => $request->gender,
            'batch' => $request->batch,
        ]);

        return response()->json([
            'success' => true,
            'message' => "Student '{$student->name}' added successfully",
            'student' => $student
        ]);
    }

    // Quick add student and assign to category
    public function quickAddToCategory(Request $request)
    {
        $availableBatches = Student::getAvailableBatches();

        $request->validate([
            'name' => 'required|string|max:255',
            'gender' => 'required|in:Male,Female',
            'batch' => 'required|integer|in:' . implode(',', $availableBatches),
            'category_id' => 'required|exists:categories,id',
        ]);

        // Create the student
        $student = Student::create([
            'name' => $request->name,
            'gender' => $request->gender,
            'batch' => $request->batch,
        ]);

        // Find current assignment for the category
        $currentAssignment = \App\Models\Assignment::where('category_id', $request->category_id)
            ->where('status', 'current')
            ->first();

        if ($currentAssignment) {
            // Add student to the assignment
            \App\Models\AssignmentMember::create([
                'assignment_id' => $currentAssignment->id,
                'student_id' => $student->id,
                'is_coordinator' => false
            ]);

            $categoryName = \App\Models\Category::find($request->category_id)->name;
            return response()->json([
                'success' => true,
                'message' => "Student '{$student->name}' added to system and assigned to {$categoryName}",
                'student' => $student
            ]);
        } else {
            return response()->json([
                'success' => true,
                'message' => "Student '{$student->name}' added to system (no current assignment found for category)",
                'student' => $student
            ]);
        }
    }

    // Get all students for deletion
    public function getAllForDeletion()
    {
        $students = Student::all();

        $students2025 = $students->where('batch', 2025)->values();
        $students2026 = $students->where('batch', 2026)->values();

        return response()->json([
            'success' => true,
            'students2025' => $students2025,
            'students2026' => $students2026
        ]);
    }

    // Delete multiple students
    public function deleteMultiple(Request $request)
    {
        $request->validate([
            'student_ids' => 'required|array',
            'student_ids.*' => 'exists:students,id'
        ]);

        $students = Student::whereIn('id', $request->student_ids)->get();
        $studentNames = $students->pluck('name')->toArray();

        // Remove from all assignment_members first (due to foreign key constraints)
        \App\Models\AssignmentMember::whereIn('student_id', $request->student_ids)->delete();

        // Then delete the students
        Student::whereIn('id', $request->student_ids)->delete();

        $count = count($request->student_ids);
        $namesList = implode(', ', $studentNames);

        return response()->json([
            'success' => true,
            'message' => "Successfully deleted {$count} student(s) from system: {$namesList}"
        ]);
    }
}