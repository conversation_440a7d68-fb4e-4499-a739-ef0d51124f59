<?php

namespace App\Http\Controllers;

use App\Models\Student;
use App\Models\Category;
use App\Models\Assignment;
use App\Models\AssignmentMember;
use App\Models\TaskChecklist;
use Illuminate\Http\Request;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index() {
        // Clean expired comments first
        AssignmentMember::cleanExpiredComments();

        // Fetch all categories with ONLY CURRENT assignments and assignment members (with students)
        $categories = Category::with(['assignments' => function($query) {
            $query->where('status', 'current');
        }, 'assignments.assignmentMembers.student'])->get();

        // Fetch all students
        $students = Student::all();

        // Fetch active batches that have students
        $activeBatches = \App\Models\Batch::active()
            ->whereHas('students')
            ->get();

        // Fetch only current assignments for the main table (to avoid N/A duplicates)
        $assignments = Assignment::with(['category', 'assignmentMembers.student'])
            ->where('status', 'current')
            ->orderBy('start_date', 'desc')
            ->get();

        // Assignment history (for View History modal - includes both current and previous)
        $assignmentHistory = Assignment::with(['category', 'assignmentMembers.student'])
            ->orderBy('status', 'asc') // current first, then previous
            ->orderBy('id', 'desc') // newest first within same status
            ->get();

        // Pass all data to the dashboard view
        return view('dashboard', compact('categories', 'students', 'assignments', 'assignmentHistory', 'activeBatches'));
    }

    public function taskChecklist()
    {
        // Get current week start (Monday)
        $currentWeekStart = Carbon::now()->startOfWeek();

        // Initialize default tasks if they don't exist for this week
        $this->initializeDefaultTasks($currentWeekStart);

        // Get all tasks for current week
        $tasks = TaskChecklist::where('week_start_date', $currentWeekStart)->get();

        return view('task-checklist', compact('tasks', 'currentWeekStart'));
    }

    public function updateTaskStatus(Request $request)
    {
        $task = TaskChecklist::findOrFail($request->task_id);

        if ($request->week == 1) {
            $status = $task->week1_status ?? array_fill(0, 7, null);
            $status[$request->day] = $request->status;
            $task->week1_status = $status;
        } else {
            $status = $task->week2_status ?? array_fill(0, 7, null);
            $status[$request->day] = $request->status;
            $task->week2_status = $status;
        }

        $task->save();

        return response()->json(['success' => true]);
    }

    public function updateTaskRemarks(Request $request)
    {
        $task = TaskChecklist::findOrFail($request->task_id);

        if ($request->week == 1) {
            $task->week1_remarks = $request->remarks;
        } else {
            $task->week2_remarks = $request->remarks;
        }

        $task->save();

        return response()->json(['success' => true]);
    }

    public function updateWeekDates(Request $request)
    {
        $weekStart = Carbon::parse($request->week_start_date)->startOfWeek();

        // Update all tasks for this week
        TaskChecklist::where('week_start_date', $weekStart)
            ->update(['week_start_date' => $weekStart]);

        return response()->json(['success' => true]);
    }

    private function initializeDefaultTasks($weekStart)
    {
        // Check if tasks already exist for this week
        $existingTasks = TaskChecklist::where('week_start_date', $weekStart)->count();

        if ($existingTasks > 0) {
            return; // Tasks already exist
        }

        $defaultTasks = [
            // Kitchen Tasks
            ['category' => 'KITCHEN', 'description' => 'Assigned members wake up on time and completed their tasks as scheduled.'],
            ['category' => 'KITCHEN', 'description' => 'The students assigned to cook the rice completed the task properly.'],
            ['category' => 'KITCHEN', 'description' => 'The students assigned to cook the viand completed the task properly.'],
            ['category' => 'KITCHEN', 'description' => 'The students assigned to assist the cook carried out their duties diligently.'],
            ['category' => 'KITCHEN', 'description' => 'Ingredients were prepared ahead of time.'],
            ['category' => 'KITCHEN', 'description' => 'The kitchen was properly cleaned after cooking.'],
            ['category' => 'KITCHEN', 'description' => 'The food was transferred from the kitchen to the center.'],
            ['category' => 'KITCHEN', 'description' => 'Proper inventory of stocks was maintained and deliveries were handled appropriately.'],
            ['category' => 'KITCHEN', 'description' => 'Water and food supplies were regularly monitored and stored in the proper place.'],
            ['category' => 'KITCHEN', 'description' => 'Receipts, kitchen phones, and keys were safely stored.'],
            ['category' => 'KITCHEN', 'description' => 'Kitchen utensils were properly stored.'],
            ['category' => 'KITCHEN', 'description' => 'The stove was turned off after cooking.'],
            ['category' => 'KITCHEN', 'description' => 'Properly disposed of the garbage.'],

            // General Cleaning Tasks
            ['category' => 'GENERAL CLEANING', 'description' => 'Properly washed the burner.'],
            ['category' => 'GENERAL CLEANING', 'description' => 'Wiped and arranged the chiller.'],
            ['category' => 'GENERAL CLEANING', 'description' => 'Cleaned the canal after cooking.'],
            ['category' => 'GENERAL CLEANING', 'description' => 'Arranged the freezer.'],
        ];

        foreach ($defaultTasks as $task) {
            TaskChecklist::create([
                'task_category' => $task['category'],
                'task_description' => $task['description'],
                'week_start_date' => $weekStart,
                'week1_status' => array_fill(0, 7, null),
                'week2_status' => array_fill(0, 7, null),
            ]);
        }
    }
}