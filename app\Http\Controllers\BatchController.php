<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Batch;
use App\Models\Student;

class BatchController extends Controller
{
    // Display all batches
    public function index()
    {
        $batches = Batch::ordered()->get();
        return view('batches.index', compact('batches'));
    }

    // Store new batch
    public function store(Request $request)
    {
        $request->validate([
            'year' => 'required|integer|min:2020|max:2050|unique:batches,year',
            'name' => 'nullable|string|max:255',
        ]);

        $batch = Batch::create([
            'year' => $request->year,
            'name' => $request->name,
            'is_active' => true,
        ]);

        return response()->json([
            'success' => true,
            'message' => "Batch {$batch->year} added successfully",
            'batch' => $batch
        ]);
    }

    // Update batch
    public function update(Request $request, Batch $batch)
    {
        $request->validate([
            'name' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ]);

        $batch->update($request->only(['name', 'is_active']));

        return response()->json([
            'success' => true,
            'message' => "Batch {$batch->year} updated successfully",
            'batch' => $batch
        ]);
    }

    // Delete batch (only if no students)
    public function destroy(Batch $batch)
    {
        $studentCount = Student::where('batch', $batch->year)->count();
        
        if ($studentCount > 0) {
            return response()->json([
                'success' => false,
                'message' => "Cannot delete batch {$batch->year}. It has {$studentCount} students assigned."
            ], 400);
        }

        $year = $batch->year;
        $batch->delete();

        return response()->json([
            'success' => true,
            'message' => "Batch {$year} deleted successfully"
        ]);
    }

    // Get active batches for API
    public function getActiveBatches()
    {
        $batches = Batch::active()->get();
        return response()->json($batches);
    }
}
