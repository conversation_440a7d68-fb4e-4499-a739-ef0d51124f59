<?php


namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        // 0. Batches (run first)
        $this->call(BatchSeeder::class);

        // 1. Categories
        $now = Carbon::now();
        $categories = [
            'Kitchen',
            'Dishwashing',
            'Dining',
            'Offices & Conference Rooms',
            'Garbage, Rugs, & Rooftop',
            'Ground Floor',
        ];
        foreach ($categories as $cat) {
            DB::table('categories')->updateOrInsert([
                'name' => $cat
            ], [
                'created_at' => $now,
                'updated_at' => $now
            ]);
        }

        // 2. Students (Batch 2025)
        $batch2025_girls = [
            '<PERSON><PERSON><PERSON>, <PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>, <PERSON>','<PERSON><PERSON>, <PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>, <PERSON><PERSON>','<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>, <PERSON><PERSON>','<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>','<PERSON><PERSON>, <PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>','<PERSON><PERSON><PERSON>, <PERSON><PERSON>','<PERSON><PERSON>-as <PERSON><PERSON><PERSON>','<PERSON>, <PERSON><PERSON><PERSON>','<PERSON><PERSON>, <PERSON>','<PERSON>, <PERSON>','<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>, <PERSON>','<PERSON>, <PERSON>','<PERSON><PERSON>-<PERSON>, <PERSON>','<PERSON><PERSON><PERSON>, <PERSON> <PERSON>','<PERSON>, <PERSON> <PERSON>','<PERSON>, <PERSON><PERSON><PERSON> <PERSON>','<PERSON><PERSON>, <PERSON>','<PERSON><PERSON>, <PERSON> <PERSON>','<PERSON><PERSON>, <PERSON> <PERSON>','<PERSON><PERSON>, <PERSON> <PERSON>','<PERSON><PERSON>, <PERSON><PERSON>','<PERSON><PERSON>, <PERSON>','<PERSON>, <PERSON><PERSON>','<PERSON>, Sofia Nicole','Negrido, Dona Mae','Oco, Nicole','Pulod, Gee Ann','Rodrigo, Allyza Faith','Ruben, Jane Kyla','Sombrio, Merrydell','Tenepre, Cherry Rose','Veligaño, Cheed Loraine','Vergara, Jessa Mae','Villaester, Angela Mae','Wandasan, Rosana Jane','Ysulan, Valerie'
        ];
        $batch2025_boys = [
            'Agsalud, Radel','Apawan, Gwyn','Baguio, Joshua','Calub, Josh Harvie','Caritan, Jincent','Casaldan, Jhon Paul','Casas, Ricky','Catibod, Jun Clark','Chavez, Mark Kevin','Condez, John Arnel','Crisostomo, Janno','Ejurango, Junrel','Estelloro, Eupe','Godinez, Renz','Jovita, Michael','Kiskisan, Nathaniel','Mendoza, Deniel','Milabo, Angelito','Montaño, Jenvier','Novicio, Freddie','Pagunsan, Alfe','Paner, Dion','Parrocho, Angelo','Ricacho, Norkent','Sarmiento, Eduard Jhon','Tio, Fierce Vladimir','Ybañez, Jasper Drake'
        ];
        foreach ($batch2025_girls as $name) {
            DB::table('students')->updateOrInsert([
                'name' => $name,
                'batch' => 2025
            ], [
                'gender' => 'Female',
                'created_at' => $now,
                'updated_at' => $now
            ]);
        }
        foreach ($batch2025_boys as $name) {
            DB::table('students')->updateOrInsert([
                'name' => $name,
                'batch' => 2025
            ], [
                'gender' => 'Male',
                'created_at' => $now,
                'updated_at' => $now
            ]);
        }

        // 3. Students (Batch 2026)
        $batch2026_girls = [
            'Bawic, Mariel Ann','Bugais, Angela Lourene','Cañares, Reynelyn','Edaño, Luane Mari','Eria, Ereca Joy','Faburada, Jassy','Fernandez, Hazel Mae','Gelbolingo, Danica','Lagaras, Joanna Joy','Lumayaga, Allysa Joyce','Rellita, Neziel Jean','Barro, Niña Kathleen','Belia, Justine Mae','Canillo, Joan','Cantarona, Monique','Diaz, Chris Jane','Dignos, Precious','Gamboa, Gina','Gesim, Jella Mae','Gumanoy, Athena Grazia','Judico, Kristel Jean','Panangganan, Angelica','Silorio, Mariel','Torrechilla, Judy Mae','Veliganio, Kristel','Villadolid, Anna Rhea','Yncierto, Nadezhda Jade','Sacnanas, Mary Gwen','Sigbat, Cherry Ann','Soco, Angel Marie','Villadolid, Aiza','Villarosa, Michelle Ann'
        ];
        $batch2026_boys = [
            'Arellano, Vinzon','Cernal, Vhenz','Diendo, Justine','Dimpas, Mohammad','Escoro, Evans Est','Goles, Julius','Jagonoy, Zhards Nathan','Monion, Elgine','Montano, Adriane','Orozco, John Paul','Pacunla, Carlo','Pila, Jhon Xander','Alaud, Ivan Justine','Cisneros, Myko','Engaña, Riel Jake','Erebias, Ronald','Escala, Joshua','Fabrigar, Adrian','Gilles, Christopher','Hemoros, Rex','Jabagat, Seth Andrey','Mansanades, John Michael','Navarro, Kent John','Naveo, Rohann James Matt','Pacilan, Vince','Reboquio, Albert','Ruales, Klint','Sabandal, Milven','Solon, Bryle','Torres, John Mark','Vicaran, Edemar','Quijano, Van Johanzel','Reyes, Josef Dean Gerald','Ursal, Jasper','Virtudazo, Cristian'
        ];
        foreach ($batch2026_girls as $name) {
            DB::table('students')->updateOrInsert([
                'name' => $name,
                'batch' => 2026
            ], [
                'gender' => 'Female',
                'created_at' => $now,
                'updated_at' => $now
            ]);
        }
        foreach ($batch2026_boys as $name) {
            DB::table('students')->updateOrInsert([
                'name' => $name,
                'batch' => 2026
            ], [
                'gender' => 'Male',
                'created_at' => $now,
                'updated_at' => $now
            ]);
        }

        // 4. Category Limits
        $categoryLimits = [
            ['category_name' => 'Kitchen', 'max_total' => 32, 'max_boys' => 20, 'max_girls' => 12],
            ['category_name' => 'Dining', 'max_total' => 20, 'max_boys' => null, 'max_girls' => null],
            ['category_name' => 'Dishwashing', 'max_total' => 20, 'max_boys' => null, 'max_girls' => null],
            ['category_name' => 'Offices & Conference Rooms', 'max_total' => 20, 'max_boys' => null, 'max_girls' => null],
            ['category_name' => 'Garbage, Rugs, & Rooftop', 'max_total' => 20, 'max_boys' => null, 'max_girls' => null],
            ['category_name' => 'Ground Floor', 'max_total' => 18, 'max_boys' => null, 'max_girls' => null],
        ];
        foreach ($categoryLimits as $limit) {
            DB::table('category_limits')->updateOrInsert(
                ['category_name' => $limit['category_name']],
                [
                    'max_total' => $limit['max_total'],
                    'max_boys' => $limit['max_boys'],
                    'max_girls' => $limit['max_girls'],
                    'created_at' => $now,
                    'updated_at' => $now
                ]
            );
        }
    }
}