<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Student;
use App\Models\AssignmentMember;

class RemoveStudent extends Command
{
    protected $signature = 'student:remove {name}';
    protected $description = 'Remove a student from all assignments and database';

    public function handle()
    {
        $name = $this->argument('name');
        
        // Find students with similar names
        $students = Student::where('name', 'like', "%{$name}%")->get();
        
        if ($students->isEmpty()) {
            $this->error("No students found with name containing '{$name}'");
            return;
        }
        
        foreach ($students as $student) {
            $this->info("Removing student: {$student->name}");
            
            // Remove from all assignment_members first
            AssignmentMember::where('student_id', $student->id)->delete();
            
            // Then delete the student
            $student->delete();
            
            $this->info("Successfully removed {$student->name}");
        }
        
        $this->info("Completed removing {$students->count()} student(s)");
    }
}
