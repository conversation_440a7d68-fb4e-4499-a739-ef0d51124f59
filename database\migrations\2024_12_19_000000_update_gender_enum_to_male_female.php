<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, update existing data
        DB::statement("UPDATE students SET gender = 'Male' WHERE gender = 'boy'");
        DB::statement("UPDATE students SET gender = 'Female' WHERE gender = 'girl'");
        
        // Then modify the enum to use the new values
        DB::statement("ALTER TABLE students MODIFY COLUMN gender ENUM('Male', 'Female') NOT NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert data back to old format
        DB::statement("UPDATE students SET gender = 'boy' WHERE gender = 'Male'");
        DB::statement("UPDATE students SET gender = 'girl' WHERE gender = 'Female'");
        
        // Revert enum to old values
        DB::statement("ALTER TABLE students MODIFY COLUMN gender ENUM('boy', 'girl') NOT NULL");
    }
};
